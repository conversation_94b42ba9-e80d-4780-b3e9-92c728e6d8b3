package com.boryou.framework.config;

import io.undertow.server.DefaultByteBufferPool;
import io.undertow.websockets.jsr.WebSocketDeploymentInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServletServerContainerFactoryBean;

/**
 * Undertow 配置类
 * 解决 WebSocket 缓冲池警告问题：UT026010: Buffer pool was not set on WebSocketDeploymentInfo
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class UndertowConfig {

    /**
     * 自定义 Undertow 服务器配置
     * 主要用于配置 WebSocket 的缓冲池，解决启动时的警告信息
     */
    @Bean
    public WebServerFactoryCustomizer<UndertowServletWebServerFactory> undertowCustomizer() {
        return factory -> {
            factory.addDeploymentInfoCustomizers(deploymentInfo -> {
                // 创建自定义的字节缓冲池
                // 参数说明：
                // - directBuffers: true 使用直接内存，性能更好
                // - bufferSize: 16384 (16KB) 每个缓冲区的大小，与配置文件保持一致
                // - maxPoolSize: 512 最大池大小，根据并发需求调整
                // - threadLocalCacheSize: 12 线程本地缓存大小
                DefaultByteBufferPool bufferPool = new DefaultByteBufferPool(
                    true,    // 使用直接内存
                    16384,   // 16KB 缓冲区大小
                    512,     // 最大池大小
                    12       // 线程本地缓存大小
                );

                // 获取或创建 WebSocket 部署信息
                WebSocketDeploymentInfo wsDeploymentInfo =
                    (WebSocketDeploymentInfo) deploymentInfo.getServletContextAttributes()
                        .get(WebSocketDeploymentInfo.ATTRIBUTE_NAME);

                if (wsDeploymentInfo == null) {
                    wsDeploymentInfo = new WebSocketDeploymentInfo();
                    deploymentInfo.addServletContextAttribute(
                        WebSocketDeploymentInfo.ATTRIBUTE_NAME,
                        wsDeploymentInfo
                    );
                }

                // 设置缓冲池 - 这是解决警告的关键配置
                wsDeploymentInfo.setBuffers(bufferPool);

                log.info("✅ WebSocket 缓冲池配置完成 - 缓冲区大小: {}KB, 最大池大小: {}, 直接内存: {}",
                    16, 512, true);
            });
        };
    }

    /**
     * WebSocket 容器配置
     * 配置 WebSocket 连接的各种参数，优化性能和稳定性
     */
    @Bean
    public ServletServerContainerFactoryBean createWebSocketContainer() {
        ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();

        // 设置最大会话空闲超时时间（60秒）
        // 如果在这个时间内没有收到任何消息（包括心跳），连接将被关闭
        container.setMaxSessionIdleTimeout(60000L);

        // 设置异步发送超时时间（10秒）
        // 异步发送消息的超时时间
        container.setAsyncSendTimeout(10000L);

        // 设置文本消息缓冲区大小（1MB）
        // 支持发送大量文本数据
        container.setMaxTextMessageBufferSize(1024 * 1024);

        // 设置二进制消息缓冲区大小（1MB）
        // 支持发送二进制数据（如文件）
        container.setMaxBinaryMessageBufferSize(1024 * 1024);

        log.info("✅ WebSocket 容器配置完成 - 文本缓冲区: 1MB, 二进制缓冲区: 1MB, 空闲超时: 60s, 异步发送超时: 10s");

        return container;
    }

    /**
     * 额外的 Undertow 性能优化配置
     * 进一步优化服务器性能
     */
    @Bean
    public WebServerFactoryCustomizer<UndertowServletWebServerFactory> undertowPerformanceCustomizer() {
        return factory -> {
            // 设置 Undertow 的其他性能参数
            factory.addBuilderCustomizers(builder -> {
                // 启用 HTTP/2 支持（如果需要）
                // builder.setServerOption(UndertowOptions.ENABLE_HTTP2, true);

                log.info("✅ Undertow 性能优化配置完成");
            });
        };
    }
}
