package com.boryou.service;

import com.boryou.domain.SFile;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boryou.dto.EncryptFileDTO;
import com.boryou.query.FileQuery;
import org.springframework.web.multipart.MultipartFile;

import javax.crypto.NoSuchPaddingException;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【file】的数据库操作Service
 * @createDate 2024-01-03 14:08:43
 */
public interface SFileService extends IService<SFile> {

    /**
     * 普通文件上传
     *
     * @param file
     * @return
     * @throws IOException
     */
    String uploadFile(MultipartFile file) throws Exception;

    /**
     * 文件传输上传文件
     *
     * @param file
     * @return
     * @throws IOException
     */
    String uploadFileSecret(MultipartFile file) throws Exception;

    /**
     * 处理存在系统数据库中的 base64 文件，解密后上传到 minio
     */
    void dealOldFile() throws UnsupportedEncodingException, NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException;

    /**
     * 文件传输列表
     *
     * @param query
     * @return
     */
    List<SFile> fileList(FileQuery query);

    /**
     * 下载 压缩并加密的文件
     *
     * @param encryptFileDTO
     * @param response
     */
    void encryptFile(EncryptFileDTO encryptFileDTO, HttpServletResponse response) throws Exception;

    List<SFile> getUrlByFileIds(Long[] fileIds);


    void downloadFile(String id, HttpServletResponse response) throws Exception;
}
