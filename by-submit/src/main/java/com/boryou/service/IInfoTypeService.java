package com.boryou.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boryou.domain.InfoType;
import com.boryou.domain.ViewInfoTypeDept;
import com.boryou.query.TypeQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【info_type】的数据库操作Service
 * @createDate 2023-12-29 15:30:06
 */
public interface IInfoTypeService extends IService<InfoType> {

    /**
     * 新增类别
     *
     * @param infoType
     * @return
     */
    boolean saveOrUpdate(InfoType infoType);

    /**
     * 类别列表
     *
     * @param query
     * @return
     */
    List<ViewInfoTypeDept> typeList(TypeQuery query);

    /**
     * 删除类别
     *
     * @param id
     * @return
     */
    boolean delete(Long id);
}
