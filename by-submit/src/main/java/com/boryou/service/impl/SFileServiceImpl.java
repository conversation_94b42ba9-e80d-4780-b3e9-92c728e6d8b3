package com.boryou.service.impl;

import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.constant.Constants;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.core.redis.RedisCache;
import com.boryou.common.utils.PageUtil;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.StringUtils;
import com.boryou.common.utils.file.FileUploadUtils;
import com.boryou.common.utils.file.FileUtils;
import com.boryou.common.utils.file.MinioUtil;
import com.boryou.domain.SFile;
import com.boryou.dto.EncryptFileDTO;
import com.boryou.framework.util.AESUtils;
import com.boryou.mapper.SFileMapper;
import com.boryou.query.FileQuery;
import com.boryou.service.SFileService;
import com.boryou.upload.domain.BUploadFile;
import com.boryou.upload.domain.FileDownloadRelation;
import com.boryou.upload.mapper.BUploadFileMapper;
import com.boryou.upload.mapper.FileDownloadRelationMapper;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.MinioClient;
import io.minio.http.Method;
import lombok.RequiredArgsConstructor;
import net.lingala.zip4j.ZipFile;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.model.enums.AesKeyStrength;
import net.lingala.zip4j.model.enums.EncryptionMethod;
import org.apache.commons.compress.utils.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【file】的数据库操作Service实现
 * @createDate 2024-01-03 14:08:43
 */
@Service
@RequiredArgsConstructor
public class SFileServiceImpl extends ServiceImpl<SFileMapper, SFile> implements SFileService {

    @Value("${minio.bucket-name}")
    private String bucket;

    private final BUploadFileMapper bUploadFileMapper;
    private final MinioClient minioClient;
    private final RedisCache redisCache;
    private final FileDownloadRelationMapper downloadRelationMapper;

    @Override
    public String uploadFile(MultipartFile file) throws Exception {

        // 解密文件
        try {
            file = AESUtils.decryptFile(file);
        } catch (Exception e) {
            log.error("文件解密失败，请确认上传的文件是否为加密文件。", e);
        }

        return uploadThenSave(file, null, Constants.FILE_NORMAL);
    }

    @Override
    public String uploadFileSecret(MultipartFile file) throws Exception {

        // 解密文件
        try {
            file = AESUtils.decryptFile(file);
        } catch (Exception e) {
            log.error("文件解密失败，请确认上传的文件是否为加密文件。", e);
        }

        return uploadThenSave(file, null, Constants.FILE_WJCS);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void dealOldFile() throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidKeyException {
        // 处理base64
        final String OLD_KEY = "sectrans65322613boryou2010871305";
        byte[] raw = OLD_KEY.getBytes(StandardCharsets.UTF_8);
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, skeySpec);

        recursion(0L, cipher);

        // 处理下载的本地文件
        String rootFolder = "D:\\下载\\upload\\upload";
        java.io.File folder = new java.io.File(rootFolder);
        List<BUploadFile> uploadFiles = bUploadFileMapper.selectList(Wrappers.<BUploadFile>lambdaQuery()
                .isNull(BUploadFile::getBase64)
        );
        recursion(folder, uploadFiles);
    }

    @Override
    public List<SFile> fileList(FileQuery query) {

        LambdaQueryWrapper<SFile> wrapper = Wrappers.<SFile>lambdaQuery()
                .like(StringUtils.isNotEmpty(query.getFileName()), SFile::getOriginalName, query.getFileName())
                .eq(StringUtils.isNotEmpty(query.getFileType()), SFile::getFileType, query.getFileType())
                .eq(SFile::getStatus, Constants.ENABLE)
                .eq(SFile::getFlag, Constants.FILE_WJCS)
                .orderByDesc(SFile::getCreateTime);

        // 如果不是管理员就只能查看自己上传的和自己能下载的文件
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        boolean isAdmin = userId == 1;

        if (!isAdmin) {
            List<FileDownloadRelation> relationList = downloadRelationMapper.selectList(Wrappers.<FileDownloadRelation>lambdaQuery()
                    .eq(FileDownloadRelation::getDownloadUserId, userId)
            );
            List<Long> collect = relationList.stream().map(FileDownloadRelation::getDownloadFileId).collect(Collectors.toList());
            wrapper.nested(x -> x.eq(SFile::getCreateUserId, userId).or().in(!collect.isEmpty(), SFile::getId, collect));
        }

        PageUtil.startPage();
        return list(wrapper);

    }

    // 递归处理本地文件的上传
    private void recursion(java.io.File folder, List<BUploadFile> uploadFiles) {
        if (folder.isDirectory()) {
            java.io.File[] files = folder.listFiles();

            if (files != null) {
                for (java.io.File file : files) {
                    if (file.isDirectory()) {
                        recursion(file, uploadFiles);
                    } else {
                        String name = file.getName();
                        Optional<BUploadFile> first = uploadFiles.stream().filter(x -> x.getAddress().endsWith(name)).findFirst();
                        first.ifPresent(x -> {
                            try {
                                // 上传到 minio
                                uploadThenSave(new MockMultipartFile("file", x.getFileName(), x.getFileType(), FileUtils.readFileToByteArray(file)), x, Constants.FILE_WJCS);
                            } catch (IOException e) {
                                throw new RuntimeException(e);
                            }
                        });
                    }
                }
            }
        }
    }

    // 递归处理base64解密上传业务逻辑
    private void recursion(Long id, Cipher cipher) {
        List<BUploadFile> bUploadFiles = bUploadFileMapper.selectList(Wrappers.<BUploadFile>lambdaQuery()
                .isNotNull(BUploadFile::getBase64)
                .orderByAsc(BUploadFile::getUploadTime)
                .gt(BUploadFile::getId, id)
                // .eq(BUploadFile::getId, 1744555841966596154L)
                .last("limit 10")
        );

        // 终结条件
        if (bUploadFiles.isEmpty()) {
            return;
        } else {
            recursion(bUploadFiles.get(bUploadFiles.size() - 1).getId(), cipher);
        }

        bUploadFiles.forEach(x -> {
            byte[] encryptedBytes = Base64.getDecoder().decode(x.getBase64());
            try {
                // 解密
                byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
                // 将加密后的字节去除前缀，否则写入的文件都是base64编码的字符串
                String str = new String(decryptedBytes);
                String substring = str.substring(str.indexOf("base64,") + 7);
                decryptedBytes = Base64.getDecoder().decode(substring);
                // 将解密后的文件重新转成 MultipartFile
                MockMultipartFile file = new MockMultipartFile("file", x.getFileName(), x.getFileType(), decryptedBytes);
                // 上传到 minio
                uploadThenSave(file, x, Constants.FILE_WJCS);
            } catch (IllegalBlockSizeException | BadPaddingException | IOException e) {
                throw new RuntimeException(e);
            }
        });


    }

    /**
     * 上传 minio 然后返回文件 id
     *
     * @param file
     * @param uploadFile
     * @param flag       文件标识：1-文件传输文件；2-普通上传文件
     * @return
     * @throws IOException
     */
    private String uploadThenSave(MultipartFile file, BUploadFile uploadFile, Integer flag) throws IOException {
        // 上传到 minio
        String fileName = FileUploadUtils.uploadMinio(file);

        SFile item = new SFile();
        if (ObjectUtils.isNotEmpty(uploadFile)) {
            item.setId(String.valueOf(uploadFile.getId()));
            item.setCreateTime(uploadFile.getUploadTime());
        }
        item.setOriginalName(file.getOriginalFilename());
        item.setPath(fileName.substring(fileName.lastIndexOf(bucket) + bucket.length()));
        item.setFileName(FileUtils.getName(fileName));
        item.setFileType(fileName.substring(fileName.lastIndexOf(".") + 1));
        item.setStatus(Constants.ENABLE);
        item.setFileSize((int) file.getSize() / 1024);
        // 判断是文件传输上传还是普通上传
        if (flag != null) {
            item.setFlag(flag);
            // 没有点确定按钮的时候,将文件状态设置为禁用
            if (flag == Constants.FILE_WJCS) {
                item.setStatus(Constants.DISABLE);
            }
        }

        // 设置创建人信息
        SysUser user = SecurityUtils.getLoginUser().getUser();
        item.setCreateUserId(user.getUserId());
        item.setCreateUserName(user.getUserName());

        this.save(item);
        return item.getId();
    }

    @Override
    public void encryptFile(EncryptFileDTO encryptFileDTO, HttpServletResponse response) throws Exception {
        SFile file = this.getById(encryptFileDTO.getId());

        // 无加密狗用户需要短信验证才能下载
        String phoneNum = SecurityUtils.getLoginUser().getUser().getPhonenumber();
        String msgCode = encryptFileDTO.getCode();
        if (StrUtil.isAllNotEmpty(msgCode, phoneNum) && Validator.isMobile(phoneNum)) {
            String code = redisCache.getCacheObject("downloadFile_" + phoneNum);
            // 短信验证
            if (StringUtils.isEmpty(code) || !msgCode.equals(code)) {
                throw new Exception("短信验证失败");
            }
        } else {
            // 新狗验证
            if (StrUtil.isNotEmpty(encryptFileDTO.getNewKey()) && !encryptFileDTO.getNewKey().equals(Constants.DEFIN_EKEY)) {
                throw new Exception("新加密狗验证失败");
            }
        }


        String objectName = file.getPath();

        InputStream stream = MinioUtil.download(bucket, objectName);
        OutputStream output = response.getOutputStream();

        java.io.File temp = new java.io.File(file.getOriginalName());
        // 将 InputStream 写入临时文件
        try (FileOutputStream fos = new FileOutputStream(temp)) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = stream.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
            }
        }

        // 压缩并设置密码，并将结果写入响应输出流
        ZipParameters zipParameters = new ZipParameters();
        zipParameters.setEncryptFiles(true);
        zipParameters.setEncryptionMethod(EncryptionMethod.AES);
        zipParameters.setAesKeyStrength(AesKeyStrength.KEY_STRENGTH_256);

        List<java.io.File> filesToAdd = new ArrayList<>();
        filesToAdd.add(temp);

        String tempZipName = "temp.zip";
        String password = RandomUtil.randomNumbers(6);
        ZipFile zipFile = new ZipFile(tempZipName, password.toCharArray());
        zipFile.addFiles(filesToAdd, zipParameters);

        // 设置响应头
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(file.getOriginalName().substring(0, file.getOriginalName().lastIndexOf(".")) + ".zip", "UTF-8"));
        response.setHeader("decryptPass", AESUtils.encrypt(password));

        // zip 转输出流
        FileInputStream fileInputStream = new FileInputStream(tempZipName);
        IOUtils.copy(fileInputStream, output);
        fileInputStream.close();
        stream.close();
        output.close();

        // 删除临时文件
        new java.io.File(tempZipName).delete();
        temp.delete();
    }

    @Override
    public List<SFile> getUrlByFileIds(Long[] fileIds) {
        List<SFile> files = this.list(Wrappers.<SFile>lambdaQuery()
                .in(SFile::getId, Arrays.asList(fileIds))
                .eq(SFile::getStatus, Constants.ENABLE)
        );

        // 修改文件名以实现一次性访问

        files.forEach(x -> {
            try {
                // 生成预览链接，一天后过期
                x.setUrl(minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                        .method(Method.GET)
                        .bucket(bucket)
                        .object(x.getPath())
                        .expiry(24 * 60 * 60)
                        .build()));
            } catch (Exception e) {
                log.error("获取文件预览地址失败", e);
            }
        });
        return files;
    }

    @Override
    public void downloadFile(String id, HttpServletResponse response) throws Exception {
        SFile file = this.getById(id);
        String objectName = file.getFileName();

        InputStream stream = MinioUtil.download(bucket, file.getPath());
        ServletOutputStream output = response.getOutputStream();

        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(objectName, "UTF-8"));
        response.setContentType("application/octet-stream");
        response.setCharacterEncoding("UTF-8");
        org.apache.commons.io.IOUtils.copy(stream, output);
    }
}




