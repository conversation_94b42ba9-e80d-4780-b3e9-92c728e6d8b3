package com.boryou.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.constant.Constants;
import com.boryou.common.core.domain.entity.SysDept;
import com.boryou.common.core.domain.entity.SysRole;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.PageUtil;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.StringUtils;
import com.boryou.domain.InfoType;
import com.boryou.domain.ViewInfoTypeDept;
import com.boryou.mapper.InfoTypeMapper;
import com.boryou.mapper.ViewInfoTypeDeptMapper;
import com.boryou.query.TypeQuery;
import com.boryou.service.IInfoTypeService;
import com.boryou.system.mapper.SysDeptMapper;
import com.boryou.system.mapper.SysRoleMapper;
import com.boryou.system.mapper.SysUserMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【info_type】的数据库操作Service实现
 * @createDate 2023-12-29 15:30:06
 */
@Service
@RequiredArgsConstructor
public class InfoTypeServiceImpl extends ServiceImpl<InfoTypeMapper, InfoType> implements IInfoTypeService {

    private final SysDeptMapper deptMapper;
    private final SysRoleMapper roleMapper;
    private final SysUserMapper userMapper;
    private final ViewInfoTypeDeptMapper viewInfoTypeDeptMapper;

    @Override
    public boolean saveOrUpdate(InfoType infoType) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();

        List<ViewInfoTypeDept> infoTypes = this.typeList(new TypeQuery());
        if (infoTypes.stream().map(InfoType::getTypeName).collect(Collectors.toList()).contains(infoType.getTypeName())) {
            throw new CustomException("该部门下已存在该类别");
        }

        infoType.setCreateUserId(userId);
        infoType.setCreateUserName(user.getUserName());
        infoType.setEnable(Integer.valueOf(Constants.ENABLE));

        return super.saveOrUpdate(infoType);
    }

    @Override
    public List<ViewInfoTypeDept> typeList(TypeQuery query) {

        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long deptId = user.getDept().getDeptId();
        List<Integer> roleIds = roleMapper.selectRoleListByUserId(user.getUserId());

        // 审核人员可以看到所有的类别
        List<SysRole> roles = roleMapper.selectList(Wrappers.<SysRole>lambdaQuery()
                .eq(SysRole::getRoleName, "审核人员")
                .or().eq(SysRole::getRoleName, "超级管理员")
                .or().eq(SysRole::getRoleName, "处置人员")
        );

        int[] array = roles.stream()
                .mapToInt(x -> Integer.parseInt(x.getRoleId().toString()))
                .toArray();
        Optional<SysRole> czry = roles.stream().filter(x -> "处置人员".equals(x.getRoleName())).findFirst();

        LambdaQueryWrapper<ViewInfoTypeDept> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ViewInfoTypeDept::getEnable, Constants.ENABLE);
        wrapper.like(StringUtils.isNotEmpty(query.getTypeName()), ViewInfoTypeDept::getTypeName, query.getTypeName());
        wrapper.orderByDesc(ViewInfoTypeDept::getCreateTime);

        // 处置人员可以看到所在组织架构下所有类别
        if (czry.isPresent() && roleIds.contains(Integer.valueOf(czry.get().getRoleId().toString()))) {
            // 获取上级
            SysDept sysDept = deptMapper.selectOne(Wrappers.<SysDept>lambdaQuery().eq(SysDept::getDeptId, deptId));
            // 找到所有服务于该组织的分析师
            List<String> userIds = userMapper.fxsList(sysDept.getParentId());
            // 找到所有同级
            List<SysDept> sysDepts = deptMapper.selectList(Wrappers.<SysDept>lambdaQuery().eq(SysDept::getParentId, sysDept.getParentId()));
            List<Long> collect = sysDepts.stream().map(SysDept::getDeptId).collect(Collectors.toList());
            // 加上父级
            collect.add(sysDept.getParentId());
            wrapper.in(ViewInfoTypeDept::getDeptId, collect)
                    .or(!userIds.isEmpty())
                    .in(!userIds.isEmpty(), ViewInfoTypeDept::getCreateUserId, userIds);
        }
        // 超级管理员、审核人员
        else if (Arrays.stream(array).anyMatch(roleIds::contains)) {
            // 找到所有服务于该组织的分析师
            List<String> userIds = userMapper.fxsList(deptId);
            // 找到所有下级
            List<SysDept> sysDepts = deptMapper.selectList(Wrappers.<SysDept>lambdaQuery().eq(SysDept::getParentId, deptId));
            List<Long> collect = sysDepts.stream().map(SysDept::getDeptId).collect(Collectors.toList());
            // 加上本级
            collect.add(deptId);
            wrapper.and(x -> x.in(ViewInfoTypeDept::getDeptId, collect)
                    .or(!userIds.isEmpty())
                    .in(!userIds.isEmpty(), ViewInfoTypeDept::getCreateUserId, userIds));
        } else {
            // 普通用户可以看到本部门创建的和审核人员创建的
            Long relaDeptId = user.getRelaDeptId();
            // 获取上级审核人员
            List<String> userIds = userMapper.shryList(relaDeptId);
            wrapper.and(x -> x.eq(ViewInfoTypeDept::getDeptId, deptId)
                    .or(!userIds.isEmpty())
                    .in(!userIds.isEmpty(), ViewInfoTypeDept::getCreateUserId, userIds));
        }
        PageUtil.startPage();
        return viewInfoTypeDeptMapper.selectList(wrapper);
    }

    @Override
    public boolean delete(Long id) {
        return this.removeById(id);
    }
}




