package com.boryou.service;

import com.boryou.common.core.domain.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/1/5 15:19
 */
@FeignClient(name = "externalService", url = "http://192.168.10.86:36505")
public interface ExternalService {

    /**
     * 解析 url 获取数据
     *
     * @return
     */
    @PostMapping(value = "/common/info/searchAndHighlightByUrl")
    AjaxResult getSolrDataByUrl(@RequestBody Map<String, String> requestMap);


}
