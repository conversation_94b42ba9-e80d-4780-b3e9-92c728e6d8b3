package com.boryou.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;

import com.boryou.common.core.domain.SecretVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/2 14:00
 */
@Data
public class InfoType extends SecretVO implements Serializable {

    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 类别名称
     */
    private String typeName;

    /**
     * 创建人id
     */
    private Long createUserId;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 是否启用
     */
    private Integer enable;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
