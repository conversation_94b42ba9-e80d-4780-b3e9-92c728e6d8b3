package com.boryou.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/1/3 14:11
 */
@TableName(value = "file")
@Data
public class SFile implements Serializable {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 文件路径
     */
    private String path;

    /**
     * 文件名称
     */
    private String fileName;

    /**
     * 文件起始名称
     */
    private String originalName;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件大小，单位 KB
     */
    private Integer fileSize;

    /**
     * 状态标识：0-已删除（逻辑删除）；1-正常；
     */
    private String status;

    /**
     * 文件标识：1-文件传输文件；2-普通上传文件；
     */
    private Integer flag;

    /**
     * 创建人id
     */
    private Long createUserId;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField(exist = false)
    private String url;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
