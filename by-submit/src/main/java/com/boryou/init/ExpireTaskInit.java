package com.boryou.init;

import cn.hutool.core.util.ObjectUtil;
import com.boryou.common.constant.Constants;
import com.boryou.common.core.redis.RedisCache;
import com.boryou.common.utils.spring.SpringUtils;
import com.boryou.submit.domain.SubmitInfo;
import com.boryou.submit.service.ISubmitInfoService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RuntimeService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * 针对流程运行过程中的截止时间过期任务进行动态创建，以及对系统重启后的任务进行恢复
 *
 * <AUTHOR>
 * @date 2024/1/24 16:42
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class ExpireTaskInit {

    private static ExpireTaskInit expireTaskInit;

    @Resource
    private RedisCache redisCache;

    @Resource
    private RedisTemplate<Object, Object> redisTemplate;

    @Resource
    private RuntimeService runtimeService;

    @Resource
    private ISubmitInfoService submitInfoService;

    // 定时任务map
    public final static Map<String, ScheduledFuture> SCHEDULE_MAP = new HashMap<>();
    public static ScheduledExecutorService scheduledExecutor = Executors.newSingleThreadScheduledExecutor();

    private static List<Integer> glState = Arrays.asList();
    private static List<Integer> czState = Arrays.asList(/*Constants.GL_DCL, */Constants.CZ_YZP, Constants.CZ_DCL);

    // @PostConstruct
    public void recoveryTask() {
        expireTaskInit = this;
        expireTaskInit.runtimeService = this.runtimeService;
        expireTaskInit.submitInfoService = this.submitInfoService;
        expireTaskInit.redisTemplate = this.redisTemplate;
        expireTaskInit.redisCache = this.redisCache;


        Map<Object, Object> cacheMap = redisTemplate.opsForHash().entries(Constants.EXPIRE_TASK_KEY);
        if (cacheMap != null) {
            log.info("恢复截至日期任务数量:{}条", cacheMap.size());
            // 恢复定时任务
            cacheMap.forEach((instanceId, infoId) -> {
                SubmitInfo info = submitInfoService.getById(infoId.toString());
                if (null != info) {
                    // 恢复任务
                    if (ObjectUtil.isNotEmpty(info) && ObjectUtil.isNotEmpty(info.getDeadline())) {
                        suspendProcess(instanceId.toString(), info);
                    }
                }
            });
        }
    }

    public static void suspendProcess(String instanceId, SubmitInfo info) {
        // 在截止时间挂起任务
        ScheduledFuture<?> schedule = scheduledExecutor.schedule(() -> {
            try {
                //获取最新的报送信息（主要是相关人的处理状态信息），如果此处不是最新的状态，会导致
                SubmitInfo latestSubmitInfo = SpringUtils.getBean(ISubmitInfoService.class).getById(info.getId());
                if (glState.contains(latestSubmitInfo.getGlState()) || czState.contains(latestSubmitInfo.getCzState())) {
                    try {
                        expireTaskInit.runtimeService.suspendProcessInstanceById(instanceId);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    latestSubmitInfo.setGlState(Constants.GL_YGQ);
                    latestSubmitInfo.setCzState(Constants.CZ_YGQ);
                    expireTaskInit.submitInfoService.updateById(latestSubmitInfo);
                    log.info("报送id:{},标题:{},流程id:{} 挂起流程成功", latestSubmitInfo.getId(), latestSubmitInfo.getArticleTitle(), instanceId);
                }
                // 删除定时任务
                SCHEDULE_MAP.remove(instanceId);
                expireTaskInit.redisTemplate.opsForHash().delete(Constants.EXPIRE_TASK_KEY, instanceId);
            } catch (Exception e) {
                log.error("挂起流程失败", e);
            }
        }, info.getDeadline().getTime() - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
        // }, 30, TimeUnit.SECONDS);

        // 保存定时任务
        SCHEDULE_MAP.put(instanceId, schedule);
    }
}
