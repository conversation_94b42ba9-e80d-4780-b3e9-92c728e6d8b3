package com.boryou.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.utils.RedisUtil;
import com.boryou.common.utils.StringUtils;
import com.boryou.submit.vo.AreaTree;
import com.boryou.submit.vo.AreaTreeNew;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: Young
 * @Date: 2023/8/10
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AreaUtil {


    @PostConstruct
    public void init() {
        areaUtil = this;
        areaUtil.redisUtil = this.redisUtil;
    }

    private static final String URL = "http://iapi.boryou.com:16601";
    //	/area/info/{areaId}
    private static final String AREA_INFO = "/area/info/";
    //	/area/treeList/{areaId}/{deep}
    private static final String AREA_TREE = "/area/treeList/";

    private static AreaUtil areaUtil;

    @Resource
    private RedisUtil redisUtil;

    //    public static BiMap<String, String> areaNameCodeMap = HashBiMap.create();
    public static Map<String, String> codeNameMap = new HashMap<>();

    // @Scheduled(cron = "0 59 23 * * ?")
    @PostConstruct
    public void cacheAreaInfo() {
        log.debug("开始缓存地域code信息");
        AjaxResult areaR = AreaUtil.getTreeList("0", 3);
        Object data = areaR.getData();
        List<AreaTree> treeList = JSONUtil.toList(JSONUtil.parseArray(data), AreaTree.class);
        if (CollectionUtil.isNotEmpty(treeList)) {
            cacheProvinceCode(treeList);
        }
        log.debug("缓存地域信息完成");
    }

    private void cacheProvinceCode(List<AreaTree> areaTreeList) {
        for (AreaTree areaTree : areaTreeList) {
            List<AreaTree> children = areaTree.getChildren();
            if (null != children) {
                cacheProvinceCode(children);
            }
//            areaNameCodeMap.put(areaTree.getName(),areaTree.getId());
            codeNameMap.put(areaTree.getId(), areaTree.getName());
        }
    }

    public static AjaxResult getAreaInfo(String areaId) {
        String requestUrl = URL + AREA_INFO + areaId;
        HttpRequest request = HttpUtil.createGet(requestUrl);
        HttpResponse response = request.execute();
        return JSONUtil.toBean(response.body(), AjaxResult.class);
    }

    public static AjaxResult getTreeList(String areaId, Integer deep) {
        String requestUrl = URL + AREA_TREE + areaId + "/" + deep;
        HttpRequest request = HttpUtil.createGet(requestUrl);
        HttpResponse response = request.execute();
        return JSONUtil.toBean(response.body(), AjaxResult.class);
    }

    public static List<AreaTreeNew> areaTreeResult;


    private static String findParentNames(AreaTreeNew node, String targetId) {
        if (targetId.equals(node.getId())) {
            return node.getName();
        }
        if (node.getChildren() == null) {
            return null;
        }
        for (AreaTreeNew child : node.getChildren()) {
            String result = findParentNames(child, targetId);
            if (result != null) {
                return node.getName() + result;
            }
        }
        return null;
    }

    /**
     * 初始化自定义省市区
     *
     * <AUTHOR>
     * @date 2024/1/31 10:34
     **/
    @PostConstruct
    public void init1() {
        log.info("初始化自定义省市区树结构");
        ClassPathResource classPathResource = new ClassPathResource("zh_citys.json");//原因是：前端当时为了实现树的选择要求，树的格式不能是iapi的，只能我去网上找了一个json格式的使用
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            String json = objectMapper.readTree(classPathResource.getStream()).toString();
            areaTreeResult = JSONUtil.toList(json, AreaTreeNew.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String getAreaNames(String areaCode) {
        String areaNames = "";
        for (AreaTreeNew areaTreeNew : areaTreeResult) {
            if (StringUtils.isEmpty(areaNames)) {
                areaNames = findParentNames(areaTreeNew, areaCode);
            }
        }
        return areaNames;
    }
//    public List<AreaTreeNew> getAreaTreeResult() {
//        return areaTreeResult;
//    }
//
//    public static void setAreaTreeResult(List<AreaTreeNew> areaTreeResult) {
//        AreaUtil.areaTreeResult = areaTreeResult;
//    }

}
