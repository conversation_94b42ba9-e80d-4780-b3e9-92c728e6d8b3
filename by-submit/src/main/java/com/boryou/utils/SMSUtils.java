package com.boryou.utils;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/*
 *phones是多条   phone是单条
 * SIMPLE_SMS_ADDRESS  是单条   MULTI_SIMPLE_SMS_ADDRESS是多条
 * <AUTHOR>
 * @date 2024-01-23 15:16
 */
@Slf4j
public class SMSUtils {


    public static final String AUDIT_PASS_MOUDLE = "jJvK7S6AnrNhLnrbFYgDM2R9kL4Ce1MXPDABmsC0BL";
    public static final String AUDIT_REFUSE_MOUDLE = "vmH54x8lAuQbhBYH6LQX1AsxhSBu4o0KivPyYmznQT";
    public static final String AUDIT_NEW_MOUDLE = "qIYQewl0MhOEiQtZ2GPObWAuHlu7kmPjozZODtcpEf";

    private static final Map<String, String> MESSAGE_MAP = new HashMap<>();

    static {
        MESSAGE_MAP.put(AUDIT_PASS_MOUDLE, "【安徽博约】您上报的信息需修改材料，如已修改，请忽略！");
        MESSAGE_MAP.put(AUDIT_REFUSE_MOUDLE, "【安徽博约】您有一条分发信息待处理，如已处理，请忽略！");
        MESSAGE_MAP.put(AUDIT_NEW_MOUDLE, "【安徽博约】您有一条上报信息待审核，如已审核，请忽略！");
    }

    /**
     * 服务IP   多条短信发送接口地址
     */
    private static final String IP = "**************:36509";
    private static final String MULTI_SIMPLE_SMS_ADDRESS = IP + "/sms/multiSend";
    /**
     * 单条短信发送接口地址
     */
    private static final String SIMPLE_SMS_ADDRESS = IP + "/sms/send";
    /**
     * 调用短信接口验证码
     */
    private static final String SMS_VERIFY_CODE = "RAudCrWQe8LGQEORznt10Va5bdNlotZ0pgB";

    /**
     * @param phones 接收短信的手机号, ctime创建时间, name活动名称
     * <AUTHOR>
     * @description 审核通过提醒
     * @date 2022-08-23
     */
    public static String sendEdit(List<String> phones) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("type", AUDIT_PASS_MOUDLE);
        jsonObject.set("phones", phones);
        String body = HttpUtil.createPost(MULTI_SIMPLE_SMS_ADDRESS)
                .body(jsonObject.toString())
                .header("Authorization", SMS_VERIFY_CODE)
                .execute()
                .body();
        log.info("发送短信:{},电话:{},结果：{}", MESSAGE_MAP.get(AUDIT_PASS_MOUDLE), phones.toString(), body);
        return body;
    }

    /**
     * @param phones 接收短信的手机号, ctime创建时间, name活动名称
     * <AUTHOR>
     * @description 待处理
     * @date 2022-08-23
     */
    public static String sendUndo(List<String> phones) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("type", AUDIT_REFUSE_MOUDLE);
        jsonObject.set("phones", phones);
        String body = HttpUtil.createPost(MULTI_SIMPLE_SMS_ADDRESS)
                .body(jsonObject.toString())
                .header("Authorization", SMS_VERIFY_CODE)
                .execute()
                .body();
        log.info("发送短信:{},电话:{},结果：{}", MESSAGE_MAP.get(AUDIT_REFUSE_MOUDLE), phones.toString(), body);
        return body;
    }

    /**
     * @param phones 接收短信的手机号, ctime创建时间, name活动名称
     * <AUTHOR>
     * @description 待管理员审核短信
     * @date 2022-08-23
     */
    public static String sendApproval(List<String> phones) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("type", AUDIT_NEW_MOUDLE);
        jsonObject.set("phones", phones);
        String body = HttpUtil.createPost(MULTI_SIMPLE_SMS_ADDRESS).body(jsonObject.toString())
                .header("Authorization", SMS_VERIFY_CODE).execute().body();
        log.info("发送短信:{},电话:{},结果：{}", MESSAGE_MAP.get(AUDIT_NEW_MOUDLE), phones.toString(), body);
        return body;
    }

}

