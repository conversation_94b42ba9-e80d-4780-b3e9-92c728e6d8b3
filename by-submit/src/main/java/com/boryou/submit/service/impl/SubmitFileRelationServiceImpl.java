package com.boryou.submit.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.domain.SFile;
import com.boryou.manage.domain.File;
import com.boryou.manage.service.FileService;
import com.boryou.service.SFileService;
import com.boryou.submit.domain.SubmitFileRelation;
import com.boryou.submit.mapper.SubmitFileRelationMapper;
import com.boryou.submit.service.ISubmitFileRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 信息报送附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
@Service
public class SubmitFileRelationServiceImpl extends ServiceImpl<SubmitFileRelationMapper, SubmitFileRelation> implements ISubmitFileRelationService {
    @Autowired
    @Lazy
    private SFileService fileService;

    /**
     * 查询信息报送附件
     *
     * @param id 信息报送附件ID
     * @return 信息报送附件
     */
    @Override
    public SubmitFileRelation selectSubmitFileRelationById(Long id) {
        return baseMapper.selectSubmitFileRelationById(id);
    }

    /**
     * 查询信息报送附件列表
     *
     * @param submitFileRelation 信息报送附件
     * @return 信息报送附件
     */
    @Override
    public List<SubmitFileRelation> selectSubmitFileRelationList(SubmitFileRelation submitFileRelation) {
        return baseMapper.selectSubmitFileRelationList(submitFileRelation);
    }


    /**
     * 新增信息报送附件
     *
     * @param submitFileRelation 信息报送附件
     * @return 结果
     */
    @Override
    public int insertSubmitFileRelation(SubmitFileRelation submitFileRelation) {
        return baseMapper.insertSubmitFileRelation(submitFileRelation);
    }

    /**
     * 修改信息报送附件
     *
     * @param submitFileRelation 信息报送附件
     * @return 结果
     */
    @Override
    public int updateSubmitFileRelation(SubmitFileRelation submitFileRelation) {
        return baseMapper.updateSubmitFileRelation(submitFileRelation);
    }

    /**
     * 批量删除信息报送附件
     *
     * @param ids 需要删除的信息报送附件ID
     * @return 结果
     */
    @Override
    public int deleteSubmitFileRelationByIds(String[] ids) {
        if (null != ids && ids.length > 0) {
            return baseMapper.deleteSubmitFileRelationByIds(ids);
        }
        return 0;
    }

    @Override
    public int deleteSubmitFileRelationByFileIds(String[] ids) {
        if (null != ids && ids.length > 0) {
            UpdateWrapper<SubmitFileRelation> queryWrapper = new UpdateWrapper<>();
            queryWrapper.in("file_id", ids);
            return baseMapper.delete(queryWrapper);
        }
        return 0;
    }

    /**
     * 删除信息报送附件信息
     *
     * @param id 信息报送附件ID
     * @return 结果
     */
    @Override
    public int deleteSubmitFileRelationById(Long id) {
        return baseMapper.deleteSubmitFileRelationById(id);
    }

    @Override
    public List<SFile> getFilesList(String id) {
        LambdaQueryWrapper<SubmitFileRelation> queryWraper = new LambdaQueryWrapper<>();
        queryWraper.eq(SubmitFileRelation::getInfoId, id);
        queryWraper.isNull(SubmitFileRelation::getProcessId);
        List<SubmitFileRelation> list = this.list(queryWraper);
        List<SFile> files = new ArrayList<>();
        for (SubmitFileRelation fileRelation : list) {
            SFile file = fileService.getById(fileRelation.getFileId());
            if (file == null) {
                continue;
            }
            files.add(file);
        }
        return files;
    }

}
