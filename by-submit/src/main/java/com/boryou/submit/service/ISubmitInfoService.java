package com.boryou.submit.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.TreeSelect;
import com.boryou.submit.domain.SubmitFileRelation;
import com.boryou.submit.domain.SubmitInfo;
import com.boryou.submit.domain.SubmitProcess;
import com.boryou.submit.dto.ApprovalDTO;
import com.boryou.submit.dto.SubmitInfoAddDTO;
import com.boryou.submit.dto.SubmitInfoDTO;
import org.activiti.engine.task.Task;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * 信息报送Service接口
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
public interface ISubmitInfoService extends IService<SubmitInfo> {
    /**
     * 查询信息报送
     *
     * @param id 信息报送ID
     * @return 信息报送
     */
    SubmitInfo selectSubmitInfoById(Long id);

    /**
     * 查询信息报送列表
     *
     * @param submitInfo 信息报送
     * @return 信息报送集合
     */
    List<SubmitInfo> selectSubmitInfoList(SubmitInfo submitInfo);

    /**
     * 新增信息报送
     *
     * @param submitInfo 信息报送
     * @return 结果
     */
    int insertSubmitInfo(SubmitInfoAddDTO submitInfo);

    void saveManagerSubmit(SubmitInfoAddDTO submitInfo);

    void saveFxsSubmit(SubmitInfoAddDTO submitInfo);

    /**
     * 修改信息报送
     *
     * @param submitInfo 信息报送
     * @return 结果
     */
    int updateSubmitInfo(SubmitInfoAddDTO submitInfo);

    /**
     * 批量删除信息报送
     *
     * @param ids 需要删除的信息报送ID
     * @return 结果
     */
    int deleteSubmitInfoByIds(Long[] ids);

    /**
     * 删除信息报送信息
     *
     * @param id 信息报送ID
     * @return 结果
     */
    int deleteSubmitInfoById(Long id);

    /**
     * 加载处置者人员    管理者和处置者专用接口
     **/
    List<TreeSelect> loadProcessor(SubmitInfo submitInfo);

    List<LinkedHashMap<String, String>> loadSubmitUsers();


    AjaxResult export(SubmitInfoDTO submitInfo);

    int validPermission();

    boolean approvalSubmit(ApprovalDTO approvalDTO);

    void updateState(String id, Integer fxsState, Integer glState, Integer czState);

    void addAttachmentFiles(List<SubmitFileRelation> files, String infoId, String processId);

    List<TreeSelect> loadApprover();

    List<TreeSelect> loadManager();

    Integer getUserRole();

    void deleteRelaFiles(SubmitInfoAddDTO submitInfo);


    List<TreeSelect> loadManagerDept();

    void chaoshi(String id, String deadline, String deadlineNum);

    void noProcessSubmit(SubmitInfo submitinfo, Task task, SubmitProcess submitProcess);

    void applySupply(SubmitInfo submitinfo, Task task, List<SubmitFileRelation> files, SubmitProcess submitProcess);

    void directProcess(SubmitInfo submitinfo, Task task);

    void upSubmit(ApprovalDTO approvalDTO, SubmitInfo submitinfo, Task task, SubmitProcess submitProcess);

    void forwardSubmit(ApprovalDTO approvalDTO, SubmitInfo submitinfo, Task task, SubmitProcess submitProcess);

    void distributeSubmit(ApprovalDTO approvalDTO, SubmitInfo submitinfo, Task task, SubmitProcess submitProcess);

    void processSubmit(ApprovalDTO approvalDTO, SubmitInfo submitinfo, Task task, SubmitProcess submitProcess);

    void finishSubmit(SubmitInfo submitinfo, Task task, SubmitProcess submitProcess);

    void saveFlow(String infoId, String actName, Long receiverUserId, String status);

    void recordFlow(String infoId, String actName, SubmitProcess process, Integer scopeType);

    /**
     * 任务完成状态，用作综合管理展示
     *
     * @param submitInfoDTO
     * @param userId
     * @return
     */
    List<SubmitInfo> taskNum(SubmitInfoDTO submitInfoDTO, Long userId);
}
