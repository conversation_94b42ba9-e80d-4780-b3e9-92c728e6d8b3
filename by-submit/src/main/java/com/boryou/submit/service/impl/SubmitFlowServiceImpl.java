package com.boryou.submit.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.utils.DateUtils;
import com.boryou.submit.domain.SubmitFlow;
import com.boryou.submit.mapper.SubmitFlowMapper;
import com.boryou.submit.service.ISubmitFlowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 报送流程关系映射Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
@Service
public class SubmitFlowServiceImpl extends ServiceImpl<SubmitFlowMapper, SubmitFlow> implements ISubmitFlowService {
    @Autowired
    private SubmitFlowMapper submitFlowMapper;

    /**
     * 查询报送流程关系映射
     *
     * @param id 报送流程关系映射ID
     * @return 报送流程关系映射
     */
    @Override
    public SubmitFlow selectSubmitFlowById(String id) {
        return submitFlowMapper.selectSubmitFlowById(id);
    }

    /**
     * 查询报送流程关系映射列表
     *
     * @param submitFlow 报送流程关系映射
     * @return 报送流程关系映射
     */
    @Override
    public List<SubmitFlow> selectSubmitFlowList(SubmitFlow submitFlow) {
        return submitFlowMapper.selectSubmitFlowList(submitFlow);
    }

    /**
     * 新增报送流程关系映射
     *
     * @param submitFlow 报送流程关系映射
     * @return 结果
     */
    @Override
    public int insertSubmitFlow(SubmitFlow submitFlow) {
        submitFlow.setCreateTime(DateUtils.getNowDate());
        return submitFlowMapper.insertSubmitFlow(submitFlow);
    }

    /**
     * 修改报送流程关系映射
     *
     * @param submitFlow 报送流程关系映射
     * @return 结果
     */
    @Override
    public int updateSubmitFlow(SubmitFlow submitFlow) {
        return submitFlowMapper.updateSubmitFlow(submitFlow);
    }

    /**
     * 批量删除报送流程关系映射
     *
     * @param ids 需要删除的报送流程关系映射ID
     * @return 结果
     */
    @Override
    public int deleteSubmitFlowByIds(String[] ids) {
        return submitFlowMapper.deleteSubmitFlowByIds(ids);
    }

    /**
     * 删除报送流程关系映射信息
     *
     * @param id 报送流程关系映射ID
     * @return 结果
     */
    @Override
    public int deleteSubmitFlowById(String id) {
        return submitFlowMapper.deleteSubmitFlowById(id);
    }
}
