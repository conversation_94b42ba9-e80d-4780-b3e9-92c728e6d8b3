package com.boryou.submit.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 信息报送已过期状态任务
 *
 * <AUTHOR>
 * @date 2024-01-10 09:39
 */
@Component
@Slf4j
public class SubmitExpiresTask {

    /**
     * 报送过期方法
     *
     * <AUTHOR>
     * @date 2024/1/10 9:43
     **/
    // @Scheduled(cron = "0 0 0 * * ?")
//    @Scheduled(cron = "0/30 * * * * ?")
    public void expiresTask() {
        // log.info("---跑过期报送任务");
    }
}

