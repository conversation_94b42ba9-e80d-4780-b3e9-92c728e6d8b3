package com.boryou.submit.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.boryou.common.annotation.Excel;
import com.boryou.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * 信息报送附件对象 by_submit_file_relation
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("by_submit_file_relation")
public class SubmitFileRelation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    private String id;

    /**
     * 信息表id
     */
    @Excel(name = "信息主表id")
    private String infoId;

    /**
     * 报送节点过程记录表id
     */
    @Excel(name = "报送节点过程记录表id")
    private String processId;

    /**
     * 文件表id
     */
    @Excel(name = "文件表id")
    private String fileId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SubmitFileRelation that = (SubmitFileRelation) o;
        return Objects.equals(fileId, that.fileId);
    }

    public SubmitFileRelation(String fileId) {
        this.fileId = fileId;
    }

    @Override
    public int hashCode() {
        return Objects.hash(fileId);
    }
}
