package com.boryou.submit.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boryou.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 报送节点过程记录对象 by_submit_process
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("by_submit_process")
public class SubmitProcess /*extends BaseEntity*/ {
    private static final long serialVersionUID = 1L;

    /**
     * 报送节点过程记录主键ID
     */
    private String id;

    /**
     * 报送信息表主键ID
     */
    @Excel(name = "报送信息表主键ID")
    private String infoId;

    /**
     * 节点名称说明
     */
    @Excel(name = "节点名称说明")
    private String actName;


    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 发起人名称
     */
    @Excel(name = "发起人名称")
    private Long createById;

    /**
     * 评论
     */
    @Excel(name = "评论")
    private String comment;

    /**
     * 备注说明 转派至、下发至、已完结
     */
    @Excel(name = "备注说明 上报至、分发至、转派至、已处理、已完结")
    private String description;

    /**
     * 处理结果 1.无需处置 2.补充材料  3.直接处置  3.分发 4.完结
     */
    @Excel(name = "处理结果 1.无需处置 2.补充材料  3.直接处置   4审核上报 5分发  6转派  7.处置(处置者) 8.完结")
    private Integer processResult;


    @Excel(name = "0分析师 1共有")
    private Integer scopeType;
    // 加密密文
    @TableField(exist = false)
    private String encryptJson;

    //接收人id
    private Long managerUser;

    //接收人名称
    private String dispenserId;


}
