package com.boryou.submit.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boryou.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 报送流程关系映射对象 by_submit_flow
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("by_submit_flow")
public class SubmitFlow/* extends BaseEntity*/ {
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    private String id;

    /**
     * 报送id
     */
    @Excel(name = "报送id")
    private String infoId;

    /**
     * 发送人
     */
    @Excel(name = "发送人")
    private String forwardId;

    /**
     * 接收人
     */
    @Excel(name = "接收人")
    private String receiverId;

    /**
     * 节点英文名称 节点名称
     */
    @Excel(name = "节点英文名称 节点名称")
    private String actName;

    /**
     * 是否是过期数据 0否 1是
     */
    @Excel(name = "是否是过期数据 0否 1是")
    private String history;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

}
