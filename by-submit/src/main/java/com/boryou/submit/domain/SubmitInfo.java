package com.boryou.submit.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boryou.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;

import java.util.Date;
import java.util.List;


/**
 * 信息报送对象 by_submit_info
 * 分析师导出对象VO 用的也是这个
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@TableName("by_submit_info")
public class SubmitInfo {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @Id
    private String id;
    /**
     * 文章标题
     */
    @Excel(name = "文章标题")
    private String articleTitle;
    /**
     * 文章链接
     */
    @Excel(name = "文章链接")
    private String articleLink;


    /**
     * 原文时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "原文时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date originalTime;


    @TableField(exist = false)
    @Excel(name = "信息类别")
    private String infoTypeName;

    /**
     * 媒体类型
     */
    @Excel(name = "媒体类型", dictType = "media_type")
    private String mediaType;

    /**
     * 涉及地域
     */

    private String articleArea;

    /**
     * 涉及地域全路径名称
     */
    @Excel(name = "涉事地域")
    private String articleAreaName;

    @Excel(name = "信息来源")
    @TableField(exist = false)
    private String submitTypeName;
    /**
     * 倾向性
     */
    @Excel(name = "倾向性", dictType = "tendency_type")
    private String tendency;

    /**
     * 处置者名称    列表用于：报送、下发人名称
     */
    @Excel(name = "分发人")
    private String dispenser;


    @Excel(name = "状态")
    @TableField(exist = false)
    private String statusName;


    /**
     * 处理截至时间 处置截至时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    @Excel(name = "处理截止时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private Date deadline;


    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 事件描述
     */
    private String eventDesc;

    /**
     * 处置建议
     */
    private String suggestions;

    /**
     * 管理人员
     */

    private Long managerUser;

    @TableField(exist = false)
    private String managerUserName;


    // 加密密文
    @TableField(exist = false)
    private String encryptJson;


    /**
     * 创建类型 0是分析报送  1.主动下发
     */
//    @Excel(name = "创建类型 0是分析报送  1.主动下发")
//    private String createType;


    private String deadlineNum;

    /**
     * 分析师状态 -1全部 0待审核 1.待修改 2 未采纳 3已采纳
     */
    private Integer fxsState;

    /**
     * 管理者状态   -1全部 0待审核 1 待处理 2已处理3 已过期  4已完结
     */
    private Integer glState;

    /**
     * 处置者状态 -1全部 0默认无状态 1待处理 2已处理 3.已转派 4已过期 5已完结
     */
    private Integer czState;

    private Long createById;

    /**
     * 提交类型 0分析师报送 1管理者下发
     */

    private Integer submitType;


    /**
     * 处置者名称    列表用于：报送、下发人id
     */
    private String dispenserId;

    @TableField(exist = false)
    private List<Long> disposers;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 信息类别
     */
    private String infoType;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @TableField(exist = false)
    private String taskId;


//    @TableField(exist = false)
//    private List<Integer> options;

    @TableField(exist = false)
    private String assigneer;

    @TableField(exist = false)
    private int all;

    @TableField(exist = false)
    private String filter;


    @TableField(exist = false)
    private int owner;


    @TableField(exist = false)
    private boolean admin;
}
