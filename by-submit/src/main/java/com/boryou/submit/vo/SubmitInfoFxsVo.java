package com.boryou.submit.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.boryou.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024-01-04 14:35
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SubmitInfoFxsVo {
    private static final long serialVersionUID = 1L;

    /**
     * 文章标题
     */
    @Excel(name = "文章标题")
    private String articleTitle;
    /**
     * 文章链接
     */
    @Excel(name = "文章链接")
    private String articleLink;

    /**
     * 原文时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "原文时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date originalTime;


    @TableField(exist = false)
    @Excel(name = "信息类别")
    private String infoTypeName;

    /**
     * 媒体类型
     */
    @Excel(name = "媒体类型", dictType = "media_type")
    private String mediaType;

    private String articleArea;

    @Excel(name = "涉事地域")
    private String articleAreaName;

    /**
     * 倾向性
     */
    @Excel(name = "倾向性", dictType = "tendency_type")
    private String tendency;

    /**
     * 处置者名称    列表用于：报送、下发人名称
     */
    @Excel(name = "接收人")
    private String dispenser;

    @Excel(name = "状态")
    private String statusName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;


    /**
     * 处理截至时间 处置截至时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deadline;


}

