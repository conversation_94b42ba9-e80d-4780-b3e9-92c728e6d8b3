package com.boryou.submit.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AreaTreeNew implements Cloneable {
    private String id;
    private String pid;
    private Integer level;
    private String name;
    private String sname;
    private String lng;
    private String lat;
    private List<AreaTreeNew> children;

    @Override
    public AreaTreeNew clone() {
        try {
            AreaTreeNew clone = (AreaTreeNew) super.clone();
            // TODO: copy mutable state here, so the clone can't change the internals of the original
            return clone;
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }
}
