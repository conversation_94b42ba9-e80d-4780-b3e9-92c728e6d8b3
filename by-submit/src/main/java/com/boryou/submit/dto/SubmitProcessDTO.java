package com.boryou.submit.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.boryou.domain.SFile;
import com.boryou.manage.domain.File;
import com.boryou.submit.domain.SubmitProcess;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-08 10:39
 */
@Data
public class SubmitProcessDTO extends SubmitProcess {
    @TableField(exist = false)
    private List<SFile> files;

    // 加密密文
    @TableField(exist = false)
    private String encryptJson;
}

