package com.boryou.submit.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.boryou.submit.domain.SubmitInfo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-12-29 14:39
 */
@Data
public class SubmitInfoDTO extends SubmitInfo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 原文开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date selectStartTime;
    /**
     * 原文结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date selectEndTime;

    /**
     * 查询处理状态
     */
    private Integer state;
    /**
     * 排序列
     */
    private String sortColumn;
    /**
     * 排序字段  order by xxx
     */
    private String sortOrder;

    // 加密密文
    @TableField(exist = false)
    private String encryptJson;

    private String[] ids;
}

