package com.boryou.submit.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.boryou.submit.domain.SubmitFileRelation;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-01-08 16:41
 */
@Data
public class ApprovalDTO {
    /**
     * 信息报送id
     */
    private String infoId;

    private String comment;

    /**
     * 处理结果 1.无需处置 2.补充材料  3.直接处置   4审核上报 5分发  6转派  7.处置(处置者) 8.完结
     */
    private String processResult;
    /**
     * 处置者id（多个按逗号分隔）    列表用于：报送、下发人id
     */
    private String dispenserId;

    /**
     * 管理人员
     */
    private Long managerUser;

    private String deadlineNum;

    private List<SubmitFileRelation> files;


    // 加密密文
    @TableField(exist = false)
    private String encryptJson;
}

