package com.boryou.submit.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boryou.submit.domain.SubmitFlow;

import java.util.List;

/**
 * 报送流程关系映射Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
public interface SubmitFlowMapper extends BaseMapper<SubmitFlow> {
    /**
     * 查询报送流程关系映射
     *
     * @param id 报送流程关系映射ID
     * @return 报送流程关系映射
     */
    public SubmitFlow selectSubmitFlowById(String id);

    /**
     * 查询报送流程关系映射列表
     *
     * @param submitFlow 报送流程关系映射
     * @return 报送流程关系映射集合
     */
    public List<SubmitFlow> selectSubmitFlowList(SubmitFlow submitFlow);

    /**
     * 新增报送流程关系映射
     *
     * @param submitFlow 报送流程关系映射
     * @return 结果
     */
    public int insertSubmitFlow(SubmitFlow submitFlow);

    /**
     * 修改报送流程关系映射
     *
     * @param submitFlow 报送流程关系映射
     * @return 结果
     */
    public int updateSubmitFlow(SubmitFlow submitFlow);

    /**
     * 删除报送流程关系映射
     *
     * @param id 报送流程关系映射ID
     * @return 结果
     */
    public int deleteSubmitFlowById(String id);

    /**
     * 批量删除报送流程关系映射
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSubmitFlowByIds(String[] ids);
}
