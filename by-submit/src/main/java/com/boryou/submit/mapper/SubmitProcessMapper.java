package com.boryou.submit.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boryou.submit.domain.SubmitProcess;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 报送节点过程记录Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
@Mapper
public interface SubmitProcessMapper extends BaseMapper<SubmitProcess> {
    /**
     * 查询报送节点过程记录
     *
     * @param id 报送节点过程记录ID
     * @return 报送节点过程记录
     */
    public SubmitProcess selectSubmitProcessById(Long id);

    /**
     * 查询报送节点过程记录列表
     *
     * @param submitProcess 报送节点过程记录
     * @return 报送节点过程记录集合
     */
    public List<SubmitProcess> selectSubmitProcessList(SubmitProcess submitProcess);

    /**
     * 新增报送节点过程记录
     *
     * @param submitProcess 报送节点过程记录
     * @return 结果
     */
    public int insertSubmitProcess(SubmitProcess submitProcess);

    /**
     * 修改报送节点过程记录
     *
     * @param submitProcess 报送节点过程记录
     * @return 结果
     */
    public int updateSubmitProcess(SubmitProcess submitProcess);

    /**
     * 删除报送节点过程记录
     *
     * @param id 报送节点过程记录ID
     * @return 结果
     */
    public int deleteSubmitProcessById(Long id);

    /**
     * 批量删除报送节点过程记录
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSubmitProcessByIds(Long[] ids);
}
