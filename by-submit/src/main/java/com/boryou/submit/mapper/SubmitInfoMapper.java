package com.boryou.submit.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boryou.submit.domain.SubmitInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * 信息报送Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
@Mapper
public interface SubmitInfoMapper extends BaseMapper<SubmitInfo> {
    /**
     * 查询信息报送
     *
     * @param id 信息报送ID
     * @return 信息报送
     */
    public SubmitInfo selectSubmitInfoById(Long id);

    /**
     * 查询信息报送列表
     *
     * @param submitInfo 信息报送
     * @return 信息报送集合
     */
    public List<SubmitInfo> selectSubmitInfoList(SubmitInfo submitInfo);

    /**
     * 查询我的全部
     *
     * @param submitInfo 信息报送
     * @return 信息报送集合
     */
    public List<SubmitInfo> selectSubmitInfoAll(SubmitInfo submitInfo);

    /**
     * 新增信息报送
     *
     * @param submitInfo 信息报送
     * @return 结果
     */
    public int insertSubmitInfo(SubmitInfo submitInfo);

    /**
     * 修改信息报送
     *
     * @param submitInfo 信息报送
     * @return 结果
     */
    public int updateSubmitInfo(SubmitInfo submitInfo);

    /**
     * 删除信息报送
     *
     * @param id 信息报送ID
     * @return 结果
     */
    public int deleteSubmitInfoById(Long id);

    /**
     * 批量删除信息报送
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSubmitInfoByIds(Long[] ids);


    List<LinkedHashMap<String, String>> loadSubmitUsers(@Param("deptId") Long deptId, @Param("userId") Long userId);

    List<LinkedHashMap<String, String>> loadManagerUsers(Long deptId);
}
