package com.boryou.submit.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boryou.submit.domain.SubmitFileRelation;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 信息报送附件Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
@Mapper
public interface SubmitFileRelationMapper extends BaseMapper<SubmitFileRelation> {
    /**
     * 查询信息报送附件
     *
     * @param id 信息报送附件ID
     * @return 信息报送附件
     */
    public SubmitFileRelation selectSubmitFileRelationById(Long id);

    /**
     * 查询信息报送附件列表
     *
     * @param submitFileRelation 信息报送附件
     * @return 信息报送附件集合
     */
    public List<SubmitFileRelation> selectSubmitFileRelationList(SubmitFileRelation submitFileRelation);

    /**
     * 新增信息报送附件
     *
     * @param submitFileRelation 信息报送附件
     * @return 结果
     */
    public int insertSubmitFileRelation(SubmitFileRelation submitFileRelation);

    /**
     * 修改信息报送附件
     *
     * @param submitFileRelation 信息报送附件
     * @return 结果
     */
    public int updateSubmitFileRelation(SubmitFileRelation submitFileRelation);

    /**
     * 删除信息报送附件
     *
     * @param id 信息报送附件ID
     * @return 结果
     */
    public int deleteSubmitFileRelationById(Long id);

    /**
     * 批量删除信息报送附件
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteSubmitFileRelationByIds(String[] ids);
}
