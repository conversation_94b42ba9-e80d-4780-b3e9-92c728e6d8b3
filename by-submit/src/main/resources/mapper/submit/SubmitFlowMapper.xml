<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.submit.mapper.SubmitFlowMapper">

    <resultMap type="SubmitFlow" id="SubmitFlowResult">
        <result property="id" column="id"/>
        <result property="infoId" column="info_id"/>
        <result property="forwardId" column="forward_id"/>
        <result property="receiverId" column="receiver_id"/>
        <result property="actName" column="act_name"/>
        <result property="createTime" column="create_time"/>
        <result property="history" column="history"/>
    </resultMap>

    <sql id="selectSubmitFlowVo">
        select id, info_id, forward_id, receiver_id, act_name, create_time, history from by_submit_flow
    </sql>

    <select id="selectSubmitFlowList" parameterType="SubmitFlow" resultMap="SubmitFlowResult">
        <include refid="selectSubmitFlowVo"/>
        <where>
            <if test="infoId != null  and infoId != ''">and info_id = #{infoId}</if>
            <if test="forwardId != null  and forwardId != ''">and forward_id = #{forwardId}</if>
            <if test="receiverId != null  and receiverId != ''">and receiver_id = #{receiverId}</if>
            <if test="actName != null  and actName != ''">and act_name like concat('%', #{actName}, '%')</if>
            <if test="history != null  and history != ''">and history = #{history}</if>
        </where>
    </select>

    <select id="selectSubmitFlowById" parameterType="String" resultMap="SubmitFlowResult">
        <include refid="selectSubmitFlowVo"/>
        where id = #{id}
    </select>

    <insert id="insertSubmitFlow" parameterType="SubmitFlow">
        insert into by_submit_flow
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="infoId != null">info_id,</if>
            <if test="forwardId != null">forward_id,</if>
            <if test="receiverId != null">receiver_id,</if>
            <if test="actName != null">act_name,</if>
            <if test="createTime != null">create_time,</if>
            <if test="history != null">history,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="infoId != null">#{infoId},</if>
            <if test="forwardId != null">#{forwardId},</if>
            <if test="receiverId != null">#{receiverId},</if>
            <if test="actName != null">#{actName},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="history != null">#{history},</if>
        </trim>
    </insert>

    <update id="updateSubmitFlow" parameterType="SubmitFlow">
        update by_submit_flow
        <trim prefix="SET" suffixOverrides=",">
            <if test="infoId != null">info_id = #{infoId},</if>
            <if test="forwardId != null">forward_id = #{forwardId},</if>
            <if test="receiverId != null">receiver_id = #{receiverId},</if>
            <if test="actName != null">act_name = #{actName},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="history != null">history = #{history},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSubmitFlowById" parameterType="String">
        delete from by_submit_flow where id = #{id}
    </delete>

    <delete id="deleteSubmitFlowByIds" parameterType="String">
        delete from by_submit_flow where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
