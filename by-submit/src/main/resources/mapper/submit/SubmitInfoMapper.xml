<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.submit.mapper.SubmitInfoMapper">

    <resultMap type="SubmitInfo" id="SubmitInfoResult">
        <result property="id" column="id"/>
        <result property="articleLink" column="article_link"/>
        <result property="articleTitle" column="article_title"/>
        <result property="originalTime" column="original_time"/>
        <result property="infoType" column="info_type"/>
        <result property="mediaType" column="media_type"/>
        <result property="articleArea" column="article_area"/>
        <result property="tendency" column="tendency"/>
        <result property="eventDesc" column="event_desc"/>
        <result property="suggestions" column="suggestions"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createById" column="create_by_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="managerUser" column="manager_user"/>
        <result property="deadline" column="deadline"/>
        <result property="fxsState" column="fxs_state"/>
        <result property="glState" column="gl_state"/>
        <result property="czState" column="cz_state"/>
        <result property="submitType" column="submit_type"/>
    </resultMap>


    <sql id="selectSubmitInfoVo">
        select id, article_link, article_title, original_time, info_type, media_type, article_area, tendency,
        event_desc,
        suggestions, create_time, create_by, update_time, update_by, manager_user, deadline, fxs_state, gl_state,
        cz_state,article_area_name
        from by_submit_info
    </sql>


    <select id="selectSubmitInfoAll" parameterType="SubmitInfo" resultMap="SubmitInfoResult">
        select b.id,b.article_link,b.article_title,b.original_time,b.info_type,b.media_type,
        b.article_area, b.tendency, b.event_desc, b.suggestions, b.create_time, s.nick_name create_by,
        b.update_time, b.update_by, m.nick_name managerUserName, b.submit_type, b.deadline, b.fxs_state, b.gl_state,
        b.cz_state,i.type_name infoTypeName,b.article_area_name articleAreaName,b.create_by_id
        from (
        select re.BUSINESS_KEY_ from ACT_RU_TASK RES
        inner join act_hi_procinst re on re.PROC_INST_ID_=res.PROC_INST_ID_
        WHERE
        ( exists( select LINK.USER_ID_ from ACT_RU_IDENTITYLINK LINK where LINK.USER_ID_ =#{createBy} and LINK.TASK_ID_
        = RES.ID_)
        or RES.ASSIGNEE_ = #{createBy}
        <if test="owner==1">
            or RES.OWNER_ = #{createBy}
        </if>
        )
        <if test="all==1">
            union
            select re.BUSINESS_KEY_ from ACT_HI_TASKINST RES
            inner join act_hi_procinst re on re.PROC_INST_ID_=res.PROC_INST_ID_
            <if test="admin==false">
                WHERE (
                exists( select LINK.USER_ID_ from ACT_HI_IDENTITYLINK LINK where LINK.USER_ID_ = #{createBy} and
                LINK.TASK_ID_ = RES.ID_)
                or RES.ASSIGNEE_ = #{createBy}
                <if test="owner==1">
                    or RES.OWNER_ = #{createBy}
                </if>
                )
            </if>
        </if>
        )re
        inner join by_submit_info b on b.id=re.BUSINESS_KEY_
        left join sys_user s on s.user_id=b.create_by_id
        left join sys_user m on m.user_id=b.manager_user
        left join info_type i on i.id=b.info_type
        <if test="filter!=null">
            ${filter}
        </if>
        <where>
            <if test="articleLink != null  and articleLink != ''">and b.article_link = #{articleLink}</if>
            <if test="articleTitle != null  and articleTitle != ''">and b.article_title like concat('%',
                #{articleTitle}, '%')
            </if>
            <if test="infoType != null  and infoType != ''">and b.info_type = #{infoType}</if>
            <if test="mediaType != null  and mediaType != ''">and b.media_type = #{mediaType}</if>
            <!--<if test="articleArea != null  and articleArea != ''"> and b.article_area = #{articleArea}</if>-->
            <if test="tendency != null  and tendency != ''">and b.tendency = #{tendency}</if>
            <if test="eventDesc != null  and eventDesc != ''">and b.event_desc = #{eventDesc}</if>
            <if test="suggestions != null  and suggestions != ''">and b.suggestions = #{suggestions}</if>
            <!-- <if test="managerUser != null  and managerUser != ''"> and (b.manager_user = #{managerUser} or  b.create_by_id = #{managerUser})</if>-->
            <if test="managerUserName != null  and managerUserName != ''">and (b.manager_user = #{managerUserName} or
                b.create_by_id=#{managerUserName})
            </if>
            <if test="createById != null and createById != ''">and b.create_by_id = #{createById}</if>
            <if test="deadline != null ">and b.deadline = #{deadline}</if>
            <if test="articleAreaName != null and articleAreaName!=''">and b.article_area_name like concat('%',
                #{articleAreaName}, '%')
            </if>
            <if test="assigneer != null and assigneer != ''">and b.create_by_id = #{assigneer}</if>
            <if test="submitType != null">and b.submit_type = #{submitType}</if>
            <if test="infoType != null and infoType != ''">and b.info_type = #{infoType}</if>
            <choose>
                <when test="fxsState != null  and fxsState!=-1">
                    and b.fxs_state =#{fxsState}
                </when>
                <when test="fxsState != null  and fxsState ==-1">
                    and b.fxs_state is not null
                </when>
            </choose>
            <choose>
                <when test="glState != null  and glState!=-1">
                    and b.gl_state =#{glState}
                </when>
                <when test="glState != null  and glState ==-1">
                    and b.gl_state is not null
                </when>
            </choose>
            <choose>
                <when test="czState != null  and czState!=-1">
                    and b.cz_state =#{czState}
                </when>
                <when test="czState != null  and czState ==-1">
                    and b.cz_state is not null and b.cz_state!=0
                </when>
            </choose>

            <if test="selectStartTime != null"><!-- 开始时间检索 -->
                AND b.original_time &gt;= #{selectStartTime}
            </if>
            <if test="selectEndTime != null"><!-- 结束时间检索 -->
                AND b.original_time &lt;= #{selectEndTime}
            </if>
        </where>
        <choose>
            <when test="sortColumn !=null and sortColumn != '' and sortOrder !=null and sortOrder != ''">
                order by b.${sortColumn} ${sortOrder}
            </when>
            <otherwise>
                order by b.update_time desc,create_time desc
            </otherwise>
        </choose>
    </select>


    <select id="selectSubmitInfoList" parameterType="SubmitInfo" resultMap="SubmitInfoResult">
        select b.id,b.article_link,b.article_title,b.original_time,b.info_type,b.media_type,
        b.article_area, b.tendency, b.event_desc, b.suggestions, b.create_time, s.nick_name create_by,
        b.update_time, b.update_by, m.nick_name managerUserName, b.submit_type, b.deadline, b.fxs_state, b.gl_state,
        b.cz_state,b.create_by_id
        from by_submit_info b
        left join sys_user s on s.user_id=b.create_by_id
        left join sys_user m on m.user_id=b.manager_user
        <where>
            <if test="articleLink != null  and articleLink != ''">and b.article_link = #{articleLink}</if>
            <if test="articleTitle != null  and articleTitle != ''">and b.article_title = #{articleTitle}</if>
            <if test="infoType != null  and infoType != ''">and b.info_type = #{infoType}</if>
            <if test="mediaType != null  and mediaType != ''">and b.media_type = #{mediaType}</if>
            <if test="articleArea != null  and articleArea != ''">and b.article_area = #{articleArea}</if>
            <if test="tendency != null  and tendency != ''">and b.tendency = #{tendency}</if>
            <if test="eventDesc != null  and eventDesc != ''">and b.event_desc = #{eventDesc}</if>
            <if test="suggestions != null  and suggestions != ''">and b.suggestions = #{suggestions}</if>
            <if test="managerUser != null  and managerUser != ''">and b.manager_user = #{managerUser} or b.create_by_id
                = #{managerUser}
            </if>
            <if test="deadline != null ">and b.deadline = #{deadline}</if>
            <if test="createById != null and createById != ''">and b.manager_user = #{createById}</if>
            <choose>
                <when test="fxsState != null  and fxsState != '' and fxsState!=-1">
                    and b.fxs_state =#{fxsState}
                </when>
                <when test="fxsState != null  and fxsState ==-1">
                    and b.fxs_state is not null
                </when>
            </choose>
            <choose>
                <when test="glState != null  and glState != '' and glState!=-1">
                    and b.gl_state =#{glState}
                </when>
                <when test="glState != null  and glState ==-1">
                    and b.gl_state is not null
                </when>
            </choose>
            <choose>
                <when test="czState != null  and czState != '' and czState!=-1">
                    and b.cz_state =#{czState}
                </when>
                <when test="czState != null  and czState ==-1">
                    and b.cz_state is not null and b.cz_state!=0
                </when>
            </choose>

            <if test="createTimeStart != null"><!-- 开始时间检索 -->
                AND b.original_time &gt;= #{createTimeStart}
            </if>
            <if test="createTimeEnd != null"><!-- 结束时间检索 -->
                AND b.original_time &lt;= #{createTimeEnd}
            </if>
        </where>
        <choose>
            <when test="sortColumn !=null and sortColumn != '' and sortOrder !=null and sortOrder != ''">
                order by b.${sortColumn} ${sortOrder}
            </when>
            <otherwise>
                order by b.update_time desc,create_time desc
            </otherwise>
        </choose>
    </select>

    <select id="selectSubmitInfoById" parameterType="Long" resultMap="SubmitInfoResult">
        <include refid="selectSubmitInfoVo"/>
        where id = #{id}
    </select>

    <insert id="insertSubmitInfo" parameterType="SubmitInfo">
        insert into by_submit_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="articleLink != null">article_link,</if>
            <if test="articleTitle != null">article_title,</if>
            <if test="originalTime != null">original_time,</if>
            <if test="infoType != null">info_type,</if>
            <if test="mediaType != null">media_type,</if>
            <if test="articleArea != null">article_area,</if>
            <if test="tendency != null">tendency,</if>
            <if test="eventDesc != null">event_desc,</if>
            <if test="suggestions != null">suggestions,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="managerUser != null">manager_user,</if>
            <if test="deadline != null">deadline,</if>
            <if test="fxsState != null">fxs_state,</if>
            <if test="glState != null">gl_state,</if>
            <if test="czState != null">cz_state,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="articleLink != null">#{articleLink},</if>
            <if test="articleTitle != null">#{articleTitle},</if>
            <if test="originalTime != null">#{originalTime},</if>
            <if test="infoType != null">#{infoType},</if>
            <if test="mediaType != null">#{mediaType},</if>
            <if test="articleArea != null">#{articleArea},</if>
            <if test="tendency != null">#{tendency},</if>
            <if test="eventDesc != null">#{eventDesc},</if>
            <if test="suggestions != null">#{suggestions},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="managerUser != null">#{managerUser},</if>
            <if test="deadline != null">#{deadline},</if>
            <if test="fxsState != null">#{fxsState},</if>
            <if test="glState != null">#{glState},</if>
            <if test="czState != null">#{czState},</if>
        </trim>
    </insert>

    <update id="updateSubmitInfo" parameterType="SubmitInfo">
        update by_submit_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="articleLink != null">article_link = #{articleLink},</if>
            <if test="articleTitle != null">article_title = #{articleTitle},</if>
            <if test="originalTime != null">original_time = #{originalTime},</if>
            <if test="infoType != null">info_type = #{infoType},</if>
            <if test="mediaType != null">media_type = #{mediaType},</if>
            <if test="articleArea != null">article_area = #{articleArea},</if>
            <if test="tendency != null">tendency = #{tendency},</if>
            <if test="eventDesc != null">event_desc = #{eventDesc},</if>
            <if test="suggestions != null">suggestions = #{suggestions},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="managerUser != null">manager_user = #{managerUser},</if>
            <if test="deadline != null">deadline = #{deadline},</if>
            <if test="fxsState != null">fxs_state = #{fxsState},</if>
            <if test="glState != null">gl_state = #{glState},</if>
            <if test="czState != null">cz_state = #{czState},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSubmitInfoById" parameterType="Long">
        delete from by_submit_info where id = #{id}
    </delete>

    <delete id="deleteSubmitInfoByIds" parameterType="String">
        delete from by_submit_info where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>


    <select id="loadSubmitUsers" resultType="java.util.LinkedHashMap">
        select distinct s.user_id,s.nick_name from sys_user s
        inner join sys_dept d on d.dept_id=s.dept_id
        and d.dept_id=#{deptId}
        WHERE s.user_id=#{userId} and s.del_flag=0 and s.status=0
        union
        SELECT DISTINCT
        s.user_id,
        s.nick_name
        FROM
        sys_user s
        INNER JOIN sys_dept d ON d.dept_id = s.rela_dept_id
        where d.dept_id=#{deptId} and s.del_flag=0 and s.status=0
    </select>

    <select id="loadManagerUsers" resultType="java.util.LinkedHashMap">
        select distinct s.user_id,s.nick_name from sys_user s
        inner join sys_dept d on d.dept_id=s.dept_id and d.dept_id=#{deptId}
        where s.del_flag=0 and s.status=0
    </select>
</mapper>
