package com.boryou.web.util;


import redis.clients.jedis.Jedis;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;
import redis.clients.jedis.Tuple;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * redis迁移工具-单节点版本！！ 支持 指定ip，指定端口，指定密码，指定db的inde，支持指定索引前缀的迁移
 *
 * <AUTHOR>
 * @date 2024-07-22 10:11
 */
public class RedisDataMigrationSingleVersion {
    public static String keyPrefix = "yq-vsboryou:";

    public static void main(String[] args) {
        // 源和目标 Redis 实例的主机地址和端口
        String sourceHost = "************";
        int sourcePort = 6379;
        String sourcePassword = "76EVgQ1ITAMCoe8t"; // 源Redis密码

        String destHost = "************";
        int destPort = 6379;
        String destPassword = "76EVgQ1ITAMCoe8t"; // 目标Redis密码

        // 源和目标 Redis 的数据库索引
        int sourceDbIndex = 9; // 源数据库索引
        int destDbIndex = 0;   // 目标数据库索引

        // 连接到源和目标 Redis 实例，并选择相应的数据库
        Jedis source = new Jedis(sourceHost, sourcePort);
        source.auth(sourcePassword); // 认证密码
        source.select(sourceDbIndex);

        Jedis destination = new Jedis(destHost, destPort);
        destination.auth(destPassword); // 认证密码
        destination.select(destDbIndex);

        // 调用迁移方法
        migrateData(source, destination);

        // 关闭连接
        source.close();
        destination.close();
    }

    private static void migrateData(Jedis source, Jedis destination) {
        // 键的前缀和扫描参数
        String cursor = "0";
        int count = 10; // 每次扫描返回的键的数量
        ScanParams scanParams = new ScanParams().count(count).match(keyPrefix + "*");
        do {
            // 扫描源 Redis 中的键
            ScanResult<String> scanResult = source.scan(cursor, scanParams);
            List<String> keys = scanResult.getResult();

            // 遍历扫描结果中的键
            for (String key : keys) {
                String type = source.type(key);
                System.out.println("---" + key);
                switch (type) {
                    case "string":
                        String stringValue = source.get(key);
                        destination.set(key, stringValue);
                        break;
                    case "list":
                        List<String> listValues = source.lrange(key, 0, -1);
                        destination.del(key);
                        for (int i = 0; i < listValues.size(); i++) {
                            destination.lpush(key, listValues.get(i));
                        }
                        break;
                    case "set":
                        Set<String> setValues = source.smembers(key);
                        destination.del(key);
                        setValues.forEach(value -> destination.sadd(key, value));
                        break;
                    case "zset":
                        Set<Tuple> zsetValues = source.zrangeWithScores(key, 0, -1);
                        destination.del(key);
                        zsetValues.forEach(tuple -> destination.zadd(key, tuple.getScore(), tuple.getElement()));
                        break;
                    case "hash":
                        Map<String, String> hashValues = source.hgetAll(key);
                        destination.del(key);
                        hashValues.forEach((field, value) -> destination.hset(key, field, value));
                        break;
                    default:
                        System.out.println("Unsupported type for key: " + key);
                }
            }
            // 准备下一次扫描
            cursor = scanResult.getCursor();
        } while (!"0".equals(cursor));
        System.out.println("迁移完成");
    }
}

