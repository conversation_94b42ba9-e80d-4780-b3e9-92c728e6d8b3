package com.boryou.web.enums;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum CountryEnum {

    // 东亚
    JAPAN("Japan", "日本", "日本", "东京", 139.6917, 35.6895),
    SOUTH_KOREA("South Korea", "韩国", "韓國", "首尔", 126.9780, 37.5665),

    // 北亚
    RUSSIA("Russia", "俄罗斯", "俄羅斯", "莫斯科", 37.6176, 55.7558),

    // 南亚
    INDIA("India", "印度", "印度", "新德里", 77.2090, 28.6139),
    SINGAPOR<PERSON>("Singapore", "新加坡", "新加坡", "新加坡", 103.8198, 1.3522),

    // 大洋洲
    AUSTRALIA("Australia", "澳大利亚", "澳大利亞", "堪培拉", 149.1287, -35.2809),

    // 西欧
    UNITED_KINGDOM("United Kingdom", "英国", "英國", "伦敦", -0.1278, 51.5074),
    FRANCE("France", "法国", "法國", "巴黎", 2.3522, 48.8566),
    GERMANY("Germany", "德国", "德國", "柏林", 13.4050, 52.5200),
    SWITZERLAND("Switzerland", "瑞士", "瑞士", "伯尔尼", 7.4474, 46.9480),
    NETHERLANDS("Netherlands", "荷兰", "荷蘭", "阿姆斯特丹", 4.9041, 52.3676),

    // 南欧
    ITALY("Italy", "意大利", "意大利", "罗马", 12.4964, 41.9028),
    SPAIN("Spain", "西班牙", "西班牙", "马德里", -3.7038, 40.4168),

    // 北欧
    SWEDEN("Sweden", "瑞典", "瑞典", "斯德哥尔摩", 18.0686, 59.3293),
    DENMARK("Denmark", "丹麦", "丹麥", "哥本哈根", 12.5683, 55.6761),

    // 东欧
    POLAND("Poland", "波兰", "波蘭", "华沙", 21.0122, 52.2297),

    // 中东
    SAUDI_ARABIA("Saudi Arabia", "沙特阿拉伯", "沙特阿拉伯", "利雅得", 46.6753, 24.7136),

    // 北美
    UNITED_STATES("United States", "美国", "美國", "华盛顿哥伦比亚特区", -77.0369, 38.9072),
    CANADA("Canada", "加拿大", "加拿大", "渥太华", -75.6972, 45.4215),
    MEXICO("Mexico", "墨西哥", "墨西哥", "墨西哥城", -99.1332, 19.4326),

    // 南美
    BRAZIL("Brazil", "巴西", "巴西", "巴西利亚", -47.9297, -15.7801),

    // 非洲
    SOUTH_AFRICA("South Africa", "南非", "南非", "比勒陀利亚", 28.1871, -25.7469),

    UNKNOWN("", "", "", "", 0, 0),
    ;

    // 枚举字段
    private final String englishName;
    private final String chineseName;
    private final String chineseTraditionalName;
    private final String capital;
    private final double longitude;
    private final double latitude;

    // 静态方法：获取所有国家的中文和英文名称组成的List
    public static List<String> getAllCountryNames() {
        List<String> countryNames = new ArrayList<>();
        for (CountryEnum country : CountryEnum.values()) {
            if (StrUtil.isNotEmpty(country.getChineseName()) && StrUtil.isNotEmpty(country.getEnglishName())) {
                Set<String> nameSet = new HashSet<>();
                nameSet.add("\"" + country.getChineseName() + "\"");
                nameSet.add("\"" + country.getChineseTraditionalName() + "\"");
                nameSet.add("\"" + country.getCapital() + "\"");
                nameSet.add("\"" + country.getEnglishName() + "\"");
                countryNames.add(CollUtil.join(nameSet, ","));
            }
        }
        return countryNames;
    }

    public static CountryEnum getByName(String name) {
        for (CountryEnum country : CountryEnum.values()) {
            name = name.replace("\"", "");
            if (country.getChineseName().equals(name) || country.getEnglishName().equals(name) || country.getChineseTraditionalName().equals(name) || country.getCapital().equals(name)) {
                return country;
            }
        }
        return UNKNOWN;
    }

}
