package com.boryou.web.controller.bigscreen.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.boryou.common.constant.Constants;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.util.ConvertHandler;
import com.boryou.web.controller.common.util.EsSearchUtil;
import com.boryou.web.domain.OutHot;
import com.boryou.web.domain.vo.SearchVO;
import com.boryou.web.module.screen.entity.CloudWord;
import com.boryou.web.module.screen.service.ScreenCloudWordService;
import com.boryou.web.module.screen.service.ScreenService;
import com.boryou.web.module.screen.vo.CloudWordVO;
import com.boryou.web.module.screen.vo.ScreenSearchVO;
import com.boryou.web.service.ElasticsearchService;
import com.boryou.web.service.HotService;
import com.boryou.web.service.SearchAnalyseService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@RestController
public class ScreenController extends BaseController {
    private final ScreenCloudWordService screenCloudWordService;

    private final ScreenService screenService;

    private final SearchAnalyseService searchAnalyseService;

    private final HotService hotService;

    private final ConvertHandler convertHandler;

    //宁夏方案
    private final Long planId = 1864861838979481600L;

    /**
     * 青海舆情热词云图限定词列表查询
     *
     * <AUTHOR>
     * @date 2025/2/12
     */
    @GetMapping("/couldWord/page")
    public TableDataInfo page(@ModelAttribute CloudWordVO cloudWord) {
        startPage();
        return getDataTable(screenCloudWordService.list(cloudWord));
    }

    /**
     * 青海舆情热词云图限定词列表新增
     *
     * <AUTHOR>
     * @date 2025/2/12
     */
    @PutMapping("/couldWord/add")
    public AjaxResult add(@RequestBody CloudWordVO cloudWord) {
        if (StrUtil.isEmpty(cloudWord.getWord()) || screenCloudWordService.list(cloudWord).size() > 0) {
            return AjaxResult.error("关键词已存在");
        }
        cloudWord.setCreateTime(DateUtil.date());
        cloudWord.setCreateBy(SecurityUtils.getUsername());
        return AjaxResult.success(screenCloudWordService.save(cloudWord));
    }

    /**
     * 青海舆情热词云图限定词列表修改
     *
     * <AUTHOR>
     * @date 2025/2/12
     */
    @PostMapping("/couldWord/update")
    public AjaxResult update(@RequestBody CloudWord cloudWord) {
        return AjaxResult.success(screenCloudWordService.updateById(cloudWord));
    }

    /**
     * 青海舆情热词云图限定词列表删除
     *
     * <AUTHOR>
     * @date 2025/2/12
     */
    @DeleteMapping("/couldWord/del/{id}")
    public AjaxResult del(@PathVariable("id") String id) {
        List<String> ids = new ArrayList<>(Arrays.asList(id.split(",")));
        return AjaxResult.success(screenCloudWordService.removeByIds(ids));
    }

    /**
     * 青海舆情热词云图
     *
     * <AUTHOR>
     * @date 2025/2/11
     */
    @PostMapping("/screen/wordsAnalyse")
    public AjaxResult wordsAnalyse(@RequestBody ScreenSearchVO screenSearchVO) {
        SearchVO vo = convertScreenSearchVO(screenSearchVO);
        if (StrUtil.isNotEmpty(screenSearchVO.getAreaCode()) && !"100000".equals(screenSearchVO.getAreaCode())) {
            vo.setContentAreaCode(screenSearchVO.getAreaCode());
        }
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(vo);
        JSONArray words = screenService.wordAnalyse(bo);
        return AjaxResult.success(words);
    }

    /**
     * 全国传染病舆情地区分布
     *
     * <AUTHOR>
     * @date 2025/2/11
     */
    @PostMapping("/screen/countryMap")
    public AjaxResult countryMap(@RequestBody ScreenSearchVO screenSearchVO) {
        SearchVO vo = convertScreenSearchVO(screenSearchVO);
        if (StrUtil.isNotEmpty(screenSearchVO.getAreaCode())) {
            vo.setContentAreaCode(screenSearchVO.getAreaCode());
        } else {
            vo.setContentAreaCode("100000");
        }
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(vo);
        JSONArray array = searchAnalyseService.areaMap(bo);

        JSONArray result = array.getJSONObject(0).getJSONArray("zList");

        for (Object o : result) {
            JSONObject object = (JSONObject) o;
            object.putOnce("areaId", object.getStr("code"));
            object.putOnce("areaName", object.getStr("name"));
            object.putOnce("issueArticleNum", object.getStr("data"));
            object.putOnce("pointValue", object.getJSONArray("value"));
            object.remove("code");
            object.remove("name");
            object.remove("data");
            object.remove("value");

        }
        return AjaxResult.success(result);
    }

    /**
     * 全球传染病舆情地区分布
     *
     * <AUTHOR>
     * @date 2025/2/11
     */
    @PostMapping("/screen/worldMap")
    public AjaxResult worldMap(@RequestBody ScreenSearchVO screenSearchVO) {
        SearchVO vo = convertScreenSearchVO(screenSearchVO);
        vo.setType(vo.getType() + ",24");
        vo.setContentAreaCode(null);
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(vo);
        JSONArray result = screenService.worldCountMap(bo);
        return AjaxResult.success(result);
    }

    /**
     * 全球传染病舆情重点信息
     *
     * <AUTHOR>
     * @date 2025/2/12
     */
    @PostMapping("/screen/worldKeyInfo")
    public AjaxResult worldKeyInfo(@RequestBody ScreenSearchVO screenSearchVO) {
        SearchVO vo = convertScreenSearchVO(screenSearchVO);
        vo.setPlanId(planId);
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(vo);
        bo.setSearchPosition("1");
        bo.setQuadraticPosition("1");
        bo.setQuadraticWord(screenSearchVO.getSearchWord());
        bo.setContentAreaCode(null);
        bo.setType(vo.getType() + ",24");
        bo.setSortType(3);
        List<EsBean> esBeans = EsSearchUtil.searchEsBeanList(bo);

        if (CollUtil.isNotEmpty(esBeans)) {
            Map<String, EsBean> map = new HashMap<>();
            for (EsBean esBean : esBeans) {
                if (StrUtil.isNotEmpty(esBean.getTitle()) && esBean.getTitle().trim().length() > 4) {
                    String key = esBean.getTitle().trim().substring(0, 4);
                    if (!map.containsKey(key)) {
                        map.put(key, esBean);
                    }
                    if (map.entrySet().size() >= 10) {
                        break;
                    }
                }

            }
            return AjaxResult.success(map.values());
        }
        return AjaxResult.success(esBeans);
    }

    /**
     * 传染病舆情信息趋势
     *
     * <AUTHOR>
     * @date 2025/2/12
     */
    @PostMapping("/screen/infoTrend")
    public AjaxResult infoTrend(@RequestBody ScreenSearchVO screenSearchVO) {
        SearchVO vo = convertScreenSearchVO(screenSearchVO);
        if (StrUtil.isNotEmpty(screenSearchVO.getAreaCode()) && !"100000".equals(screenSearchVO.getAreaCode())) {
            vo.setContentAreaCode(screenSearchVO.getAreaCode());
        }
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(vo);
        JSONObject map = screenService.timeType(bo);
        return AjaxResult.success(map);
    }

    /**
     * 全球传染病舆情信息趋势
     *
     * <AUTHOR>
     * @date 2025/2/12
     */
    @PostMapping("/screen/worldInfoTrend")
    public AjaxResult worldInfoTrend(@RequestBody ScreenSearchVO screenSearchVO) {
        SearchVO vo = convertScreenSearchVO(screenSearchVO);
        vo.setType(vo.getType() + ",24");
        vo.setContentAreaCode(null);
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(vo);
        JSONObject map = screenService.timeType(bo);
        return AjaxResult.success(map);
    }


    /**
     * 青海传染病舆情热搜榜
     *
     * <AUTHOR>
     * @date 2025/2/12
     */
    @PostMapping("/screen/hotInfo")
    public AjaxResult hotInfo(@RequestBody(required = false) ScreenSearchVO screenSearchVO) {
        List<OutHot> hots = hotService.selectOutHotList(Collections.singletonList("nx"), new ArrayList<>(), "");
        Map<String, OutHot> map = new HashMap<>();
        for (OutHot o : hots) {
            if (o.getTitle() != null && o.getTitle().trim().length() > 4) {
                String key = o.getTitle().trim().substring(0, 4);
                if (!map.containsKey(key)) {
                    setHotInfoSource(o);
                    map.put(key, o);
                }
                if (map.entrySet().size() >= 10) {
                    break;
                }
            }
        }
        return AjaxResult.success(map.values());
    }

    /**
     * 全国传染病舆情热搜榜
     *
     * <AUTHOR>
     * @date 2025/2/12
     */
    @PostMapping("/screen/countryHotInfo")
    public AjaxResult countryHotInfo(@RequestBody(required = false) ScreenSearchVO screenSearchVO) {
        List<OutHot> hots = hotService.selectOutHotList(Collections.singletonList("100404"), new ArrayList<>(), "");
        hots.forEach(this::setHotInfoSource);
        return AjaxResult.success(hots);
    }

    private SearchVO convertScreenSearchVO(@RequestBody ScreenSearchVO screenSearchVO) {
        SearchVO vo = new SearchVO();
        vo.setSort(3);
        vo.setType("0,1,3,5,6,11,17");
        vo.setPageSize(200);
        vo.setStartTime(screenSearchVO.getStartTime());
        vo.setEndTime(screenSearchVO.getEndTime());
        vo.setKeyWord1(screenSearchVO.getSearchWord());

        if (StrUtil.isEmpty(screenSearchVO.getStartTime()) || StrUtil.isEmpty(screenSearchVO.getEndTime())) {
            vo.setTimeIndex(7);
        }
        return vo;
    }

    private void setHotInfoSource(OutHot hot) {
        if (hot.getUrl().contains("weibo")) {
            hot.setKeyword("微博");
        } else if (hot.getUrl().contains(".weixin.")) {
            hot.setKeyword("微信");
        } else if (hot.getUrl().contains(".iesdouyin.")) {
            hot.setKeyword("抖音");
        } else if (hot.getUrl().contains(".kuaishou.")) {
            hot.setKeyword("快手");
        } else if (hot.getUrl().contains(".toutiao.")) {
            hot.setKeyword("头条");
        } else if (hot.getUrl().contains(".baidu.")) {
            hot.setKeyword("百度");
        } else if (hot.getUrl().contains(".dongchedi.")) {
            hot.setKeyword("懂车帝");
        } else if (hot.getUrl().contains(".163.com")) {
            hot.setKeyword("网易");
        } else {
            hot.setKeyword("网站");
        }

    }
}
