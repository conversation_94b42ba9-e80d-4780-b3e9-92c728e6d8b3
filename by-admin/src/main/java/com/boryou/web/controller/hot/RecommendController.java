package com.boryou.web.controller.hot;

import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.domain.vo.RecommendVO;
import com.boryou.web.service.RecommendService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 热榜Controller
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@RequiredArgsConstructor
@RestController
public class RecommendController extends BaseController {

    private final RecommendService recommendService;

    /**
     * 获取推荐搜索词列表
     *
     * <p>根据当前登录用户所在部门的层级关系（包括上级和下级部门），
     * 统计这些部门中所有用户的搜索词热度，返回按搜索次数排序的推荐词列表。</p>
     *
     * <h3>功能特点：</h3>
     * <ul>
     *   <li>基于部门层级：获取当前用户所在部门的所有上级和下级部门</li>
     *   <li>跨部门统计：统计所有相关部门中用户的搜索行为</li>
     *   <li>热度排序：按搜索次数降序排列，返回最热门的搜索词</li>
     *   <li>数量限制：支持指定返回数量，默认5个，最多10个</li>
     *   <li>数据过滤：只统计状态正常且未删除的用户数据</li>
     * </ul>
     *
     * <h3>业务逻辑：</h3>
     * <ol>
     *   <li>获取当前登录用户信息</li>
     *   <li>通过用户所在部门获取部门层级关系（上级+下级）</li>
     *   <li>查询这些部门中所有用户的搜索词记录</li>
     *   <li>按搜索词分组，累加搜索次数</li>
     *   <li>按累加后的搜索次数降序排序</li>
     *   <li>返回前N个最热门的搜索词</li>
     * </ol>
     *
     * @param recommendVO 推荐请求参数
     *                    - count: 返回数量，默认5个，最大10个
     * @return AjaxResult 包含推荐搜索词列表的响应结果
     * - 成功时返回 List&lt;String&gt; 类型的搜索词列表
     * - 按搜索热度降序排列
     * @apiNote 接口路径: POST /recommend/word/list
     * <AUTHOR>
     * @since 2024-05-22
     */
    @PostMapping("/recommend/word/list")
    public AjaxResult recommendWord(@RequestBody RecommendVO recommendVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<String> words = recommendService.selectRecommendWord(recommendVO, user);
        return AjaxResult.success(words);
    }

}
