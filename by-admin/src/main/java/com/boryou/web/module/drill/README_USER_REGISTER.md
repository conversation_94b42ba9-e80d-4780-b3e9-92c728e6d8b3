# 演练用户注册功能说明

## 功能概述

在原有的签到功能基础上，新增了用户注册功能。用户扫描前端的签到码后，除了可以进行签到，还可以进行用户注册，并根据选择的角色自动加入对应的演练队伍。

## 功能特性

### 1. 用户注册
- 用户可以填写用户名、用户昵称、密码
- 支持手机号码（可选）
- 自动分配基础用户角色
- 自动关联到对应的演练任务和部门

### 2. 演练角色选择
- **红队（red）**: 用户将被自动加入红队成员列表
- **蓝队（blue）**: 用户将被自动加入蓝队成员列表  
- **游客（spectator）**: 用户可以观看演练，不加入任何队伍

### 3. 数据验证
- 用户名唯一性检查
- 手机号唯一性检查（如果提供）
- 密码强度验证
- 演练任务有效性验证

## API 接口

### 1. 用户注册接口
```
POST /drill/user/register
```

**请求参数：**
```json
{
  "drillTaskId": 1234567890,
  "userName": "testuser001",
  "nickName": "测试用户",
  "password": "123456",
  "phonenumber": "13800138000",
  "drillRole": "red"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "userId": 9876543210,
    "userName": "testuser001",
    "nickName": "测试用户",
    "drillTaskId": 1234567890,
    "drillRole": "red",
    "drillRoleName": "红队成员",
    "joinedTeam": true,
    "message": "注册成功，已加入红队！"
  }
}
```

### 2. 获取角色选项接口
```
GET /drill/role/options
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "code": "red",
      "name": "红队",
      "description": "参与攻击方演练"
    },
    {
      "code": "blue", 
      "name": "蓝队",
      "description": "参与防守方演练"
    },
    {
      "code": "spectator",
      "name": "游客",
      "description": "观看演练过程"
    }
  ]
}
```

### 3. 测试接口（开发调试用）

#### 获取演练任务信息
```
GET /drill/user/test/task/{drillTaskId}
```

#### 生成测试数据
```
GET /drill/user/test/generate-test-data/{drillTaskId}
```

#### 检查队伍状态
```
GET /drill/user/test/team-status/{drillTaskId}
```

## 数据库变更

本功能主要使用现有的数据库表结构：

1. **sys_user**: 存储用户基本信息
2. **sys_user_role**: 存储用户角色关联
3. **by_drill_task**: 演练任务表，红队和蓝队成员列表会被更新

## 业务流程

1. **用户访问注册页面**
   - 用户扫描签到码
   - 页面显示签到和注册选项

2. **填写注册信息**
   - 用户名（必填，4-20位字母数字下划线）
   - 用户昵称（必填）
   - 密码（必填，6-20位）
   - 手机号（可选，11位手机号格式）
   - 演练角色（必选：红队/蓝队/游客）

3. **系统处理注册**
   - 验证用户名和手机号唯一性
   - 创建系统用户账号
   - 分配基础用户角色
   - 根据选择的演练角色加入对应队伍

4. **返回注册结果**
   - 成功：返回用户信息和队伍加入状态
   - 失败：返回具体错误信息

## 错误处理

### 常见错误码和处理

1. **用户名已存在**
   ```json
   {
     "code": 500,
     "msg": "用户名已存在，请选择其他用户名"
   }
   ```

2. **手机号已存在**
   ```json
   {
     "code": 500,
     "msg": "手机号已存在，请使用其他手机号"
   }
   ```

3. **演练任务不存在**
   ```json
   {
     "code": 500,
     "msg": "找不到演练任务"
   }
   ```

4. **参数验证失败**
   ```json
   {
     "code": 500,
     "msg": "用户名只能包含字母、数字、下划线，长度4-20位"
   }
   ```

## 安全考虑

1. **密码加密**: 使用系统默认的密码加密方式
2. **参数验证**: 严格的前端和后端参数验证
3. **唯一性检查**: 防止重复用户名和手机号
4. **权限控制**: 新用户只分配基础权限

## 扩展性

1. **角色扩展**: 可以轻松添加新的演练角色类型
2. **验证扩展**: 可以添加更多的用户信息验证规则
3. **通知扩展**: 可以添加注册成功后的通知功能
4. **审核扩展**: 可以添加用户注册审核流程

## 使用建议

1. **测试环境**: 先使用测试接口验证功能
2. **生产环境**: 确保演练任务ID的有效性
3. **用户体验**: 前端应提供清晰的角色说明
4. **错误处理**: 前端应妥善处理各种错误情况

## 注意事项

1. 新注册的用户需要使用注册时的用户名和密码登录系统
2. 游客角色用户不会被添加到任何演练队伍中
3. 红队和蓝队用户会自动成为对应队伍的成员（非队长）
4. 注册成功后，用户信息会立即生效，可以参与演练活动
