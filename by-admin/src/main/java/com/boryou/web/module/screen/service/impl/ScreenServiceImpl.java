package com.boryou.web.module.screen.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.db.PageResult;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.boryou.web.controller.bigscreen.entity.ScreenCount;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.Word;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.util.EsSearchUtil;
import com.boryou.web.domain.vo.GraphModelVO;
import com.boryou.web.domain.vo.SearchVO;
import com.boryou.web.domain.vo.SeriesVO;
import com.boryou.web.enums.CountryEnum;
import com.boryou.web.module.screen.entity.CloudWord;
import com.boryou.web.module.screen.service.ScreenCloudWordService;
import com.boryou.web.module.screen.service.ScreenService;
import com.boryou.web.service.SearchAnalyseService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ScreenServiceImpl implements ScreenService {

    private final ScreenCloudWordService screenCloudWordService;
    private final SearchAnalyseService searchAnalyseService;

    @Override
    public JSONArray wordAnalyse(EsSearchBO bo) {
        PageResult<EsBean> esBeanList = EsSearchUtil.searchEsBeanList(bo);
        Map<String, Integer> wordsFren = new HashMap<>();
        List<Word> res = new ArrayList<>();
        List<CloudWord> wordList = screenCloudWordService.list();
        List<String> filterCloudWord = wordList.stream().map(CloudWord::getWord).collect(Collectors.toList());
//        Lexeme lexeme;
//        IKSegmenter ikSegmenter;
        for (EsBean indexResultBean : esBeanList) {
            if (StringUtils.isNotEmpty(indexResultBean.getText())) {
                String text = HtmlUtil.cleanHtmlTag(indexResultBean.getText());
                for (String s : filterCloudWord) {
                    if (text.contains(s)) {
                        int count = 0;
                        int pos = 0;
                        while ((pos = text.indexOf(s, pos)) != -1) {
                            count++;
                            pos += s.length();
                        }
                        if (wordsFren.containsKey(s)) {
                            wordsFren.put(s, wordsFren.get(s) + count);
                        } else if (count > 0) {
                            wordsFren.put(s, count);
                        }
                    }
                }
//                indexResultBean.setText(HtmlUtil.cleanHtmlTag(indexResultBean.getText()));
//                ikSegmenter = new IKSegmenter(new StringReader(indexResultBean.getText()), true);
//                try {
//                    while ((lexeme = ikSegmenter.next()) != null) {
//                        String lexemeText = lexeme.getLexemeText();
//                        if (lexemeText.length() > 1) {
//                            if (!filterCloudWord.contains(lexemeText) || TextUtil.isNumeric(lexemeText) || TextUtil.isAlphabetic(lexemeText)|| TextUtil.isSpecialCharacters(lexemeText)) {
//                                continue;
//                            }
//                            if (wordsFren.containsKey(lexemeText)) {
//                                wordsFren.put(lexemeText, wordsFren.get(lexemeText) + 1);
//                            } else {
//                                wordsFren.put(lexemeText, 1);
//                            }
//                        }
//                    }
//                } catch (IOException e) {
//                    e.printStackTrace();
//                }
            }
        }
        wordsFren.keySet().forEach(x -> {
            res.add(new Word(x, wordsFren.get(x)));
        });
        List<Word> collect = res.stream().sorted((o1, o2) -> Math.toIntExact(o2.getNum() - o1.getNum())).collect(Collectors.toList());
        if (collect.size() > 51) {
            collect = collect.subList(0, 50);
        }
        JSONArray array = new JSONArray();
        JSONObject object;
        for (Word word : collect) {
            object = JSONUtil.createObj();
            object.putOnce("name", word.getWord());
            object.putOnce("value", word.getNum());
            array.add(object);
        }
        return array;
    }

    @Override
    public JSONObject timeType(EsSearchBO bo) {
        JSONObject object = new JSONObject();
        GraphModelVO graphModelVO = searchAnalyseService.timeType(bo);
        object.putOnce("xxData", graphModelVO.getXs());
        List<List<Long>> yyData = new ArrayList<>();
        List<SeriesVO<String>> seriesList = graphModelVO.getSeriesList();
        if (CollUtil.isNotEmpty(seriesList) && CollUtil.isNotEmpty(seriesList.get(0).getData())) {
            yyData.add(seriesList.get(0).getData().stream().map(Long::valueOf).collect(Collectors.toList()));
        }
        object.putOnce("yyData", yyData);
        return object;
    }

    @Override
    public JSONArray worldCountMap(EsSearchBO bo) {
        JSONArray array = new JSONArray();
        List<String> allCountryNames = CountryEnum.getAllCountryNames();
        for (String countryName : allCountryNames) {
            bo.setKeyWord1(countryName);
            //todo  是否有其他方法替代多组关键词的多次查询
            Long infoCount = EsSearchUtil.getInfoCount(bo);
            JSONObject object = new JSONObject();
            CountryEnum byName = CountryEnum.getByName(countryName.split(",")[0]);
            object.putOnce("areaName", byName.getChineseName());
            object.putOnce("issueArticleNum", infoCount);
            object.putOnce("pointValue", new Double[]{byName.getLongitude(), byName.getLatitude(), 0.0000});
            array.add(object);
        }
        if (!array.isEmpty()) {
            // 使用 Collections.sort 进行排序
            array.sort((o1, o2) -> {
                int num1 = ((JSONObject) o1).getInt("issueArticleNum");
                int num2 = ((JSONObject) o2).getInt("issueArticleNum");
                return Integer.compare(num2, num1);
            });
        }

        return array;
    }
}
