package com.boryou.web.module.drill.controller;

import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.module.drill.domain.vo.DrillCommentLikeVO;
import com.boryou.web.module.drill.service.DrillCommentService;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class DrillCommentLikeController {

    private final DrillCommentService drillCommentService;

    /**
     * 点赞评论
     */
    @PostMapping("/comment/like/add")
    public AjaxResult commentReplyLikeAdd(@RequestBody @Validated DrillCommentLikeVO drillCommentLikeVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        return AjaxResult.success(drillCommentService.commentReplyLikeAdd(drillCommentLikeVO, user));
    }

    /**
     * 取消点赞
     */
    @PostMapping("/comment/like/cancel")
    public AjaxResult commentReplyLikeCancel(@RequestBody DrillCommentLikeVO drillCommentLikeVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        return AjaxResult.success(drillCommentService.commentReplyLikeCancel(drillCommentLikeVO, user));
    }

    /**
     * 切换点赞状态（点赞/取消点赞）
     * 新的统一接口，可以替代上面两个接口
     */
    @PostMapping("/comment/like/toggle")
    public AjaxResult toggleLike(@RequestBody @Validated DrillCommentLikeVO drillCommentLikeVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        return AjaxResult.success(drillCommentService.commentReplyLikeAdd(drillCommentLikeVO, user));
    }

}
