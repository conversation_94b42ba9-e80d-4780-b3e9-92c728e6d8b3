package com.boryou.web.module.api.service;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOptionsBuilders;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.boryou.common.exception.CustomException;
import com.boryou.web.constant.ElasticsearchIndex;
import com.boryou.web.domain.vo.AccountInfoVO;
import com.boryou.web.module.api.domain.vo.ApiAccountVO;
import com.boryou.web.module.api.enums.AuthEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class ApiService {

    private final ElasticsearchClient esClient;

    public void judgeAuth(String auth) {
        if (StrUtil.isBlankIfStr(auth)) {
            throw new CustomException("没有权限");
        }
        try {
            //权限加密 todo
            AuthEnum enumByAuth = AuthEnum.getEnumByAuth(auth);
            if (enumByAuth == null) {
                throw new CustomException("没有权限");
            }
        } catch (Exception e) {
            log.error("{} 没有权限, e: {}", auth, e.getMessage());
            throw new CustomException("没有权限");
        }

    }

    public AccountInfoVO v1Account(ApiAccountVO apiAccountVO) {
        String type = apiAccountVO.getType();
        String author = apiAccountVO.getAuthor();
        String domain = apiAccountVO.getDomain();
        String sector = apiAccountVO.getSector();
        String url = apiAccountVO.getUrl();
        String hostGet = apiAccountVO.getHost();
        String host = "";
        if (CharSequenceUtil.isNotBlank(url)) {
            host = URLUtil.url(url).getHost();
            if (CharSequenceUtil.isNotBlank(hostGet)) {
                host = host + "," + hostGet;
            }
            if (CharSequenceUtil.isNotBlank(host) && !host.startsWith("www.")) {
                host = host + ",www." + host;
            }
        }
        log.warn("ApiService.v1Account 开始,查询参数为: {}", apiAccountVO);
        AccountInfoVO accountInfoVO = new AccountInfoVO();
        if (CharSequenceUtil.isAllBlank(author, type, host, sector)) {
            return accountInfoVO;
        }
        if (CollUtil.newArrayList("1", "25", "17", "0").contains(type)) {
            //使用author不一定能查到且网站业务只显示sector
            author = "";
        }
        BoolQuery.Builder bool2 = new BoolQuery.Builder();
        if (CharSequenceUtil.isNotBlank(author)) {
            termQuery(author, "nickname.keyword", bool2);
        }
        //if (CharSequenceUtil.isNotBlank(type)) {
        //    termQuery(type, "type", bool2);
        //}
        if (CharSequenceUtil.isNotBlank(sector)) {
            termQuery(sector, "sector", bool2);
        }
        if (CharSequenceUtil.isNotBlank(host)) {
            termQuery(host, "domain", bool2);
        }
        //bool2.mustNot(Query.of(q -> q.term(t -> t.field("avatar").value(FieldValue.of("")))));
        Query query = bool2.build()._toQuery();
        try {
            SortOptions sortOptions = SortOptionsBuilders.field(f -> f.field("updateTime").order(SortOrder.Desc));
            SearchRequest searchRequest = SearchRequest.of(s -> s.index(ElasticsearchIndex.NETXMAN_ACCOUNT_INFO)
                    .query(query)
                    .size(1)
                    .sort(sortOptions));
            log.warn("ApiService.v1Account ES查询参数为: {}", searchRequest);
            SearchResponse<AccountInfoVO> searchResponse = esClient.search(searchRequest, AccountInfoVO.class);
            List<Hit<AccountInfoVO>> hits = searchResponse.hits().hits();
            for (Hit<AccountInfoVO> hit : hits) {
                accountInfoVO = hit.source();
            }
        } catch (IOException e) {
            log.error("ApiService.v1Account ES查询失败: {}", e.getMessage());
        }
        if (accountInfoVO == null) {
            return new AccountInfoVO();
        }
        String avatar = accountInfoVO.getAvatar();
        accountInfoVO.setAvatarBak(avatar);
        if (CharSequenceUtil.isNotBlank(avatar)) {
            HttpRequest httpRequest = HttpRequest.get(avatar);
            if (avatar.contains("sina")) {
                httpRequest.header("Referer", "https://www.weibo.com/");//只有微博需要加referer
            }
            try (HttpResponse httpResponse = httpRequest.timeout(5000).execute()) {
                if (httpResponse == null || !httpResponse.isOk()) {
                    accountInfoVO.setAvatar(null);
                }
            } catch (Exception e) {
                log.error("ApiService.v1Account avatar 无法访问: {}, 错误: {}", avatar, e.getMessage());
            }
        }
        return accountInfoVO;
    }

    private void termQuery(String value, String filed, BoolQuery.Builder bool) {
        if (CharSequenceUtil.isBlank(value)) {
            return;
        }
        List<FieldValue> fieldValues = CollStreamUtil.toList(CharSequenceUtil.splitTrim(value, ','), FieldValue::of);
        fieldValues.removeIf(fieldValue -> "".equals(fieldValue.stringValue()));
        if (!fieldValues.isEmpty()) {
            bool.filter(QueryBuilders.terms(t -> t.field(filed).terms(q -> q.value(fieldValues))));
        }
    }

}
