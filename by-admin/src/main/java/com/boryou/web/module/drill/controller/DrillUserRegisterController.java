package com.boryou.web.module.drill.controller;

import com.boryou.common.core.domain.AjaxResult;
import com.boryou.web.module.drill.domain.vo.DrillUserRegisterRes;
import com.boryou.web.module.drill.domain.vo.DrillUserRegisterVO;
import com.boryou.web.module.drill.service.DrillUserRegisterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 演练用户注册控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/drill/user")
@Api(tags = "演练用户注册管理")
public class DrillUserRegisterController {

    private final DrillUserRegisterService drillUserRegisterService;

    /**
     * 用户注册接口
     * 用户扫描签到码后，可以在签到页面进行注册
     * 
     * @param registerVO 注册信息
     * @return 注册结果
     */
    @PostMapping("/register")
    @ApiOperation("演练用户注册")
    public AjaxResult registerUser(@RequestBody @Valid DrillUserRegisterVO registerVO) {
        try {
            log.info("用户注册请求: 用户名={}, 昵称={}, 演练任务ID={}, 演练角色={}", 
                    registerVO.getUserName(), 
                    registerVO.getNickName(), 
                    registerVO.getDrillTaskId(), 
                    registerVO.getDrillRole());
            
            DrillUserRegisterRes result = drillUserRegisterService.registerUser(registerVO);
            
            log.info("用户注册成功: 用户ID={}, 用户名={}, 演练角色={}, 加入队伍={}", 
                    result.getUserId(), 
                    result.getUserName(), 
                    result.getDrillRoleName(), 
                    result.getJoinedTeam());
            
            return AjaxResult.success(result);
            
        } catch (Exception e) {
            log.error("用户注册失败: {}", e.getMessage(), e);
            return AjaxResult.error("注册失败: " + e.getMessage());
        }
    }
}
