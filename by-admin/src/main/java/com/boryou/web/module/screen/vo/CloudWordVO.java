package com.boryou.web.module.screen.vo;/**
 * @description
 * <AUTHOR>
 */

import com.boryou.web.module.screen.entity.CloudWord;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 *
 * <AUTHOR>
 */
@Data
public class CloudWordVO extends CloudWord {

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

}
