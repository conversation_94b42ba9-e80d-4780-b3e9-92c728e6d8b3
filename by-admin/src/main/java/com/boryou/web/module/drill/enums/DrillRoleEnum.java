package com.boryou.web.module.drill.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * 演练角色枚举
 * 
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum DrillRoleEnum {

    RED("red", "红队", "参与攻击方演练"),
    BLUE("blue", "蓝队", "参与防守方演练"),
    SPECTATOR("spectator", "游客", "观看演练过程");

    private static final Map<String, DrillRoleEnum> CODE_MAP = new HashMap<>();

    static {
        for (DrillRoleEnum roleEnum : DrillRoleEnum.values()) {
            CODE_MAP.put(roleEnum.code, roleEnum);
        }
    }

    private final String code;
    private final String name;
    private final String description;

    /**
     * 根据代码获取枚举
     * 
     * @param code 角色代码
     * @return 角色枚举
     */
    public static DrillRoleEnum getByCode(String code) {
        return CODE_MAP.get(code);
    }

    /**
     * 验证角色代码是否有效
     * 
     * @param code 角色代码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return CODE_MAP.containsKey(code);
    }
}
