package com.boryou.web.module.drill.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.RedisUtil;
import com.boryou.system.service.ISysUserService;
import com.boryou.web.constant.RedisConstant;
import com.boryou.web.module.drill.domain.*;
import com.boryou.web.module.drill.domain.vo.*;
import com.boryou.web.module.drill.enums.*;
import com.boryou.web.module.drill.service.DrillTimerService.TimerCompletionCallback;
import com.boryou.web.module.drill.util.DrillStageNavigationUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DrillService implements TimerCompletionCallback {


    private final DrillProcessStageService drillProcessStageService;
    private final DrillTaskService drillTaskService;
    private final DrillStageService drillStageService;
    private final DrillCommentService drillCommentService;
    private final DrillWebsocketService drillWebsocketService;
    private final DrillStageTimeService drillStageTimeService;
    private final DrillScoreService drillScoreService;
    private final DrillCommentReplyService drillCommentReplyService;
    private final DrillCommentLikeService drillCommentLikeService;
    private final RedisUtil redisUtil;
    private final ISysUserService userService;
    private final DrillTimerService drillTimerService;

    @Transactional(rollbackFor = Exception.class)
    public DrillProcessRes drillProcessStart(DrillProcessVO drillProcessVO, SysUser user) {

        Long drillTaskId = drillProcessVO.getDrillTaskId();
        // 1. 查询任务
        DrillTask drillTask = drillTaskService.getDrillTaskByIdWithThrow(drillTaskId);
        List<String> deptIdList = drillTaskService.getAllDeptIdWithThrow(user);
        DrillTaskVO drillTaskVO = drillTaskService.drillTaskQueryOne(drillTask, deptIdList, user);
        DateTime date = DateUtil.date();

        drillWebsocketService.drillJoinUser(drillTask, user);

        DrillProcessRes drillProcessRes = BeanUtil.copyProperties(drillTaskVO, DrillProcessRes.class);

        List<DrillProcessStage> drillProcessStageList = drillProcessStageService.lambdaQuery()
                .eq(DrillProcessStage::getDrillTaskId, drillTaskId)
                .eq(DrillProcessStage::getStageShow, true)
                .list();
        // boolean scoreFlag = false;
        // 2.更新任务状态
        Integer status = drillTaskVO.getStatus();
        // 未开始时将当前阶段全部备份, 后面这条任务使用备份表
        if (Objects.equals(status, StageStatusEnum.NOT_BEGIN.getCode())) {
            // 未开始时上一阶段和当前阶段都为空

            Long firstStageId = DrillStageNavigationUtil.getFirstStageId(drillProcessStageList);
            drillProcessRes.setNextStageId(firstStageId);

        } else if (Objects.equals(status, StageStatusEnum.RUNNING.getCode())) {
            // 进行中时上一阶段和当前阶段都为空
            Long currentStageId = DrillStageNavigationUtil.getCurrentId(drillTaskId);
            if (currentStageId == null) {
                // 说明任务一开始, 但阶段未开始
                Long firstStageId = DrillStageNavigationUtil.getFirstStageId(drillTaskId);
                drillProcessRes.setNextStageId(firstStageId);
            } else {
                Long previousStageId = DrillStageNavigationUtil.getPreviousStageId(drillTaskId, currentStageId);
                Long nextStageId = DrillStageNavigationUtil.getNextStageId(drillTaskId, currentStageId);
                drillProcessRes.setPreviousStageId(previousStageId);
                drillProcessRes.setCurrentStageId(currentStageId);
                drillProcessRes.setNextStageId(nextStageId);
                // Long lastStageId = DrillStageNavigationUtil.getLastStageId(drillTaskId);
                // if (nextStageId == null || Objects.equals(nextStageId, lastStageId)) {
                //     scoreFlag = true;
                // }
            }
        } else if (Objects.equals(status, StageStatusEnum.OVER.getCode())) {
            // 已结束时下一阶段为空
            Long lastStageId = DrillStageNavigationUtil.getLastStageId(drillTaskId);
            Long previousStageId = DrillStageNavigationUtil.getPreviousStageId(drillTaskId, lastStageId);
            drillProcessRes.setPreviousStageId(previousStageId);
            drillProcessRes.setCurrentStageId(lastStageId);
            // scoreFlag = true;
        }

        if (CollUtil.isEmpty(drillProcessStageList)) {
            throw new CustomException("演练任务阶段异常");
        }

        List<DrillProcessRes.DrillProcessStageRes> drillProcessStageRes = BeanUtil.copyToList(drillProcessStageList, DrillProcessRes.DrillProcessStageRes.class);
        drillProcessStageRes.sort(Comparator.comparingInt(DrillProcessRes.DrillProcessStageRes::getStageOrder));
        // 状态不是未开始时需要查询已发评论等 todo
        if (!Objects.equals(status, StageStatusEnum.NOT_BEGIN.getCode())) {
            List<DrillComment> drillCommentList = drillCommentService.lambdaQuery()
                    .eq(DrillComment::getDrillTaskId, drillTaskId)
                    .list();
            List<DrillCommentReply> drillCommentReplyList = drillCommentReplyService.lambdaQuery()
                    .eq(DrillCommentReply::getDrillTaskId, drillTaskId)
                    .eq(DrillCommentReply::getStatus, 0)
                    .list();
            List<Long> userIdReplyList = CollStreamUtil.toList(drillCommentReplyList, DrillCommentReply::getUserId);

            List<Long> userIdList = CollStreamUtil.toList(drillCommentList, DrillComment::getUserId);
            userIdList.addAll(userIdReplyList);

            Map<Long, SysUser> userMap = getUserMapByUserIds(userIdList);

            // 将当前用户添加到userMap中，确保在获取Redis中的点赞数据时可以使用
            if (user != null && user.getUserId() != null) {
                userMap.put(user.getUserId(), user);
            }

            // 查询已点赞评论（同时考虑Redis中的最新点赞状态）
            // 注意：我们已经修改了getLikeId方法，它会检查Redis数据是否过期，并在需要时从数据库恢复
            List<Long> commentLikeId = drillCommentLikeService.getLikeId(user, drillTaskId);

            // 构建评论响应（buildCommentRes方法已经修改，会检查Redis数据是否过期）
            drillCommentService.buildCommentRes(drillCommentList, drillCommentReplyList, drillProcessStageRes, userMap, commentLikeId, user);
        }
        List<DrillStageTime> drillStageTimeList = drillStageTimeService.lambdaQuery()
                .eq(DrillStageTime::getDrillTaskId, drillTaskId)
                .list();

        buildStageTime(drillStageTimeList, drillProcessStageRes);

        drillProcessRes.setDrillProcessStageRes(drillProcessStageRes);
        return drillProcessRes;
    }

    private void buildStageTime(List<DrillStageTime> drillStageTimeList,
                                List<DrillProcessRes.DrillProcessStageRes> drillProcessStageRes) {
        if (CollUtil.isEmpty(drillStageTimeList)) {
            return;
        }

        Map<Long, List<DrillStageTime>> processStageMap = CollStreamUtil.groupByKey(drillStageTimeList, DrillStageTime::getProcessStageId);

        for (DrillProcessRes.DrillProcessStageRes drillProcessStageRe : drillProcessStageRes) {
            Long processStageId = drillProcessStageRe.getProcessStageId();
            if (!processStageMap.containsKey(processStageId)) {
                continue;
            }
            List<DrillStageTime> drillStageTimes = processStageMap.get(processStageId);
            if (CollUtil.isEmpty(drillStageTimes)) {
                continue;
            }
            Integer timerType = drillProcessStageRe.getTimerType();
            if (Objects.equals(timerType, TimerTypeEnum.SAME_TIME.getCode())) {
                drillStageTimes.sort(Comparator.comparing(DrillStageTime::getCreateTime).reversed());
                DrillStageTime drillStageTime = drillStageTimes.get(0);
                Long drillStageTimeId = drillStageTime.getDrillStageTimeId();
                Integer timerDuration = drillStageTime.getTimerDuration();

                drillProcessStageRe.setRedTimerDuration(timerDuration);
                drillProcessStageRe.setBlueTimerDuration(timerDuration);

                String key = RedisConstant.DRILL_REMAIN_TIME_CACHE + drillStageTimeId;
                String remainTimeDuration = redisUtil.get(key);
                if (CharSequenceUtil.isNotBlank(remainTimeDuration)) {
                    Integer anInt = Convert.toInt(remainTimeDuration, 0);
                    anInt = anInt < 0 ? 0 : anInt;
                    drillProcessStageRe.setRedRemainTimerDuration(anInt);
                    drillProcessStageRe.setBlueRemainTimerDuration(anInt);
                } else {
                    drillProcessStageRe.setRedRemainTimerDuration(timerDuration);
                    drillProcessStageRe.setBlueRemainTimerDuration(timerDuration);
                }

            } else if (Objects.equals(timerType, TimerTypeEnum.SEPARATELY_TIME.getCode())) {
                Map<Integer, List<DrillStageTime>> teamTypeMap = CollStreamUtil.groupByKey(drillStageTimes, DrillStageTime::getTeamType);
                teamTypeMap.forEach((teamType, v) -> {
                    v.sort(Comparator.comparing(DrillStageTime::getCreateTime).reversed());
                    if (CollUtil.isEmpty(v)) {
                        return;
                    }
                    DrillStageTime drillStageTime = v.get(0);
                    Long drillStageTimeId = drillStageTime.getDrillStageTimeId();
                    Integer timerDuration = drillStageTime.getTimerDuration();
                    Integer remainTimerDurationFinal = drillStageTime.getRemainTimerDuration();

                    String key = RedisConstant.DRILL_REMAIN_TIME_CACHE + drillStageTimeId;
                    String remainTimeDuration = redisUtil.get(key);
                    if (CharSequenceUtil.isNotBlank(remainTimeDuration)) {
                        Integer anInt = Convert.toInt(remainTimeDuration, 0);
                        remainTimerDurationFinal = anInt < 0 ? 0 : anInt;
                    }

                    if (Objects.equals(teamType, TeamTypeEnum.RED.getCode())) {
                        drillProcessStageRe.setRedTimerDuration(timerDuration);
                        drillProcessStageRe.setRedRemainTimerDuration(remainTimerDurationFinal);
                    } else if (Objects.equals(teamType, TeamTypeEnum.BLUE.getCode())) {
                        drillProcessStageRe.setBlueTimerDuration(timerDuration);
                        drillProcessStageRe.setBlueRemainTimerDuration(remainTimerDurationFinal);
                    }
                });

            }
        }

    }

    private Map<Long, SysUser> getUserMapByUserIds(List<Long> userIdList) {
        List<String> userIdListStr = Convert.toList(String.class, userIdList);
        if (CollUtil.isEmpty(userIdListStr)) {
            return MapUtil.newHashMap();
        }
        List<SysUser> userByUserIdList = userService.getUserByUserIdList(userIdListStr);
        if (CollUtil.isEmpty(userByUserIdList)) {
            return MapUtil.newHashMap();
        }
        return CollStreamUtil.toMap(userByUserIdList, SysUser::getUserId, v -> v);
    }

    /**
     * 开始阶段
     *
     * @param drillProcessStageVO 传参
     * @param user                用户
     */
    @Transactional(rollbackFor = Exception.class)
    public DrillProcessStageVO drillStageNext(DrillProcessStageVO drillProcessStageVO, SysUser user) {
        Long drillTaskId = drillProcessStageVO.getDrillTaskId();
        if (drillTaskId == null) {
            throw new CustomException("任务id不能为空");
        }

        DateTime date = DateUtil.date();

        Long newPreviousStageId = null;
        Long newCurrentStageId = null;
        Long newNextStageId = null;

        Long processStageId = drillProcessStageVO.getProcessStageId();
        Long currentStageId = drillProcessStageVO.getCurrentStageId();
        Long nextStageId = drillProcessStageVO.getNextStageId();
        if (currentStageId == null && nextStageId == null) {
            throw new CustomException("阶段id传参异常");
        } else if (currentStageId == null) {
            // 阶段开始时更新任务为进行中
            drillTaskService.lambdaUpdate()
                    .set(DrillTask::getStatus, StageStatusEnum.RUNNING.getCode())
                    .set(DrillTask::getDrillStartTime, date)
                    .eq(DrillTask::getDrillTaskId, drillTaskId)
                    .eq(DrillTask::getStatus, StageStatusEnum.NOT_BEGIN.getCode())
                    .update();
            // 最开始 更新下一阶段为进行中 更新下一阶段开始时间为当前时间
            drillProcessStageService.updateStageStatus(2, date, null, nextStageId);
            newCurrentStageId = nextStageId;

        } else if (nextStageId == null) {
            // 最后 更新当前阶段为已结束 更新当前阶段结束时间为当前时间
            drillProcessStageService.updateStageStatus(3, null, date, currentStageId);
            newCurrentStageId = currentStageId;
        } else {
            // 中间阶段 更新当前阶段为已结束 更新下一阶段为进行中
            drillProcessStageService.updateStageStatus(3, null, date, currentStageId);
            drillProcessStageService.updateStageStatus(2, date, null, nextStageId);
            newPreviousStageId = currentStageId;
            newCurrentStageId = nextStageId;
        }
        // DrillStageNavigationUtil.refreshCache(drillTaskId);

        DrillProcessStageVO build = DrillProcessStageVO.builder()
                .drillTaskId(drillTaskId)
                .previousStageId(DrillStageNavigationUtil.getPreviousStageId(drillTaskId, newCurrentStageId))
                .currentStageId(newCurrentStageId)
                .newCurrentStage(drillProcessStageService.getDrillProcessStageById(newCurrentStageId))
                .nextStageId(DrillStageNavigationUtil.getNextStageId(drillTaskId, newCurrentStageId))
                .build();
        // 阶段开始后通知需要通知的人
        drillWebsocketService.stageToUser(build, drillTaskId, user);
        return build;
    }

    public void drillCommentPublish(DrillCommentVO drillCommentVO, SysUser user) {
        Long parentCommentId = drillCommentVO.getParentCommentId();
        String commentOrderPush = "";
        if (parentCommentId != null) {
            DrillComment one = drillCommentService.lambdaQuery().eq(DrillComment::getCommentId, parentCommentId).one();
            if (one == null) {
                throw new CustomException("指令不存在");
            }
            commentOrderPush = one.getCommentOrder();
        }

        Long processStageId = drillCommentVO.getProcessStageId();
        Long scoreProcessStageId = drillCommentVO.getScoreProcessStageId();
        List<Long> list = CollUtil.newArrayList(processStageId);
        if (scoreProcessStageId != null) {
            list.add(scoreProcessStageId);
        }
        List<DrillProcessStage> drillProcessStageList = drillProcessStageService.lambdaQuery()
                .in(DrillProcessStage::getProcessStageId, list)
                .list();

        if (CollUtil.isEmpty(drillProcessStageList)) {
            throw new CustomException("阶段不存在");
        }

        Map<Long, String> map = CollStreamUtil.toMap(drillProcessStageList, DrillProcessStage::getProcessStageId, DrillProcessStage::getStageName);

        if (!map.containsKey(processStageId)) {
            throw new CustomException("阶段不存在");
        }

        String content = drillCommentVO.getContent();
        List<String> file = drillCommentVO.getFile();
        if (CharSequenceUtil.isBlank(content) && CollUtil.isEmpty(file)) {
            throw new CustomException("内容不能为空");
        }
        String commentType = drillCommentVO.getCommentType();
        String roleInfo = drillCommentVO.getRoleInfo();
        Integer teamType = drillCommentVO.getTeamType();

        if (teamType == null) {
            teamType = getTeamTypeByRoleInfo(roleInfo);
        }

        if (teamType == null) {
            throw new CustomException("没有发布权限");
        }
        // 校验是否能发此评论 todo

        Long drillTaskId = drillCommentVO.getDrillTaskId();
        DrillTask drillTask = drillTaskService.getDrillTaskByIdWithThrow(drillTaskId);
        Integer status = drillTask.getStatus();
        if (status == null || Objects.equals(status, StageStatusEnum.OVER.getCode())) {
            throw new CustomException("演练已结束, 无法发送");
        }
        if (Objects.equals(commentType, CommentEnum.COMMAND.getType())) {
            // 判断指令几
            DrillComment drillCommentOrder = drillCommentService.lambdaQuery()
                    .eq(DrillComment::getDrillTaskId, drillTaskId)
                    .eq(DrillComment::getTeamType, teamType)
                    .eq(DrillComment::getCommentType, CommentEnum.COMMAND.getType())
                    .orderByDesc(DrillComment::getCommentOrder)
                    .last("limit 1")
                    .one();
            String commentOrder = "1";
            if (drillCommentOrder != null) {
                String commentOrderPre = drillCommentOrder.getCommentOrder();
                commentOrder = NumberUtil.add(commentOrderPre, "1").toString();
            }
            drillCommentVO.setCommentOrder(commentOrder);
        }

        Long userId = user.getUserId();
        DrillComment drillComment = BeanUtil.copyProperties(drillCommentVO, DrillComment.class);
        drillComment.setTeamType(teamType);
        Long commentId = drillComment.getCommentId();
        if (commentId == null) {
            long snowflakeNextId = IdUtil.getSnowflakeNextId();
            drillComment.setCommentId(snowflakeNextId);
        }
        drillComment.setUserId(userId);

        // 不需要打分
        // Map<String, DrillScore> drillScoreMap = drillScoreService.getDrillScoreMap(true);
        // DrillScore drillScore = drillScoreMap.get(commentType);
        // // CommentScoreEnum enumByType = CommentScoreEnum.getEnumByType(commentType);
        // if (drillScore != null) {
        //     String score = drillScore.getScore();
        //     drillComment.setScore(score);
        // }
        DateTime date = DateUtil.date();
        drillComment.setCreateTime(date);
        drillCommentService.saveOrUpdate(drillComment);

        String userName = user.getUserName();
        String nickName = user.getNickName();
        String avatar = user.getAvatar();

        String stageName = map.get(processStageId);

        DrillCommentPushVO drillCommentPushVO = BeanUtil.copyProperties(drillComment, DrillCommentPushVO.class);
        drillCommentPushVO.setStageName(stageName);

        String scoreStageName = map.get(scoreProcessStageId);
        drillCommentPushVO.setScoreStageName(scoreStageName);

        drillCommentPushVO.setUserName(userName);
        drillCommentPushVO.setNickName(nickName);
        drillCommentPushVO.setAvatar(avatar);

        if (!Objects.equals(commentType, CommentEnum.COMMAND.getType()) && CharSequenceUtil.isNotBlank(commentOrderPush)) {
            drillCommentPushVO.setCommentOrder(commentOrderPush);
        }

        // 发布完成后通知需要通知的人 先推送所有人
        drillWebsocketService.commentToUser(drillCommentPushVO, drillTaskId, user);
    }

    private Integer getTeamTypeByRoleInfo(String roleInfo) {
        if (CharSequenceUtil.isBlank(roleInfo)) {
            return null;
        }
        RoleEnum enumByCode = RoleEnum.getEnumByCode(roleInfo);
        if (enumByCode != null) {
            return enumByCode.getTeamType();
        }
        return null;
    }

    public void drillProcessLeave(DrillProcessVO drillProcessVO, Long userId) {
        Long drillTaskId = drillProcessVO.getDrillTaskId();
        drillWebsocketService.drillLeaveUser(drillTaskId, userId);
    }

    public void drillProcessAllLeave(DrillProcessVO drillProcessVO) {
        Long drillTaskId = drillProcessVO.getDrillTaskId();
        drillWebsocketService.drillAllLeave(drillTaskId);
    }

    public List<DrillProcessRes.DrillProcessStageRes.DrillCommentRes> drillCommentQuery(DrillCommentVO drillCommentVO, SysUser user) {
        Long drillTaskId = drillCommentVO.getDrillTaskId();
        if (drillTaskId == null) {
            throw new CustomException("任务id不能为空");
        }
        Long processStageId = drillCommentVO.getProcessStageId();
        String commentType = drillCommentVO.getCommentType();
        String roleInfo = drillCommentVO.getRoleInfo();
        Integer teamType = getTeamTypeByRoleInfo(roleInfo);

        List<DrillComment> drillCommentList = drillCommentService.lambdaQuery()
                .eq(DrillComment::getDrillTaskId, drillTaskId)
                .eq(processStageId != null, DrillComment::getProcessStageId, processStageId)
                .eq(CharSequenceUtil.isNotBlank(commentType), DrillComment::getCommentType, commentType)
                .eq(teamType != null, DrillComment::getTeamType, teamType)
                .orderByDesc(DrillComment::getCreateTime)
                .list();
        return BeanUtil.copyToList(drillCommentList, DrillProcessRes.DrillProcessStageRes.DrillCommentRes.class);
    }

    public void drillTimerStart(DrillStageTimerVO drillStageTimerVO, SysUser user) {
        Long drillTaskId = drillStageTimerVO.getDrillTaskId();
        Long processStageId = drillStageTimerVO.getProcessStageId();
        Integer timerType = drillStageTimerVO.getTimerType();
        Integer teamType = drillStageTimerVO.getTeamType();

        // 参数验证
        if (Objects.equals(timerType, TimerTypeEnum.SEPARATELY_TIME.getCode()) && teamType == null) {
            throw new CustomException("分开计时模式下队伍类型不能为空");
        }

        // 检查倒计时冲突
        validateTimerConflicts(drillTaskId, processStageId, timerType, teamType);

        DateTime date = DateUtil.date();

        Integer timerDuration = drillStageTimerVO.getTimerDuration();
        Integer remainTimerDuration = drillStageTimerVO.getRemainTimerDuration();
        if (remainTimerDuration == null) {
            remainTimerDuration = timerDuration;
        }

        DrillStageTime drillStageTime = new DrillStageTime();
        long snowflakeNextId = IdUtil.getSnowflakeNextId();
        drillStageTime.setDrillStageTimeId(snowflakeNextId);
        drillStageTime.setProcessStageId(processStageId);
        drillStageTime.setDrillTaskId(drillTaskId);
        drillStageTime.setTimerType(timerType);
        drillStageTime.setTeamType(teamType);
        drillStageTime.setTimerDuration(timerDuration);
        drillStageTime.setRemainTimerDuration(remainTimerDuration);
        drillStageTime.setCreateTime(date);
        drillStageTimeService.save(drillStageTime);

        drillStageTimerVO.setDrillStageTimeId(snowflakeNextId);
        drillStageTimerVO.setRemainTimerDuration(remainTimerDuration);

        // 使用新的倒计时服务启动倒计时
        drillTimerService.startTimer(drillStageTimerVO, user, this);

        log.info("倒计时启动成功: drillTaskId={}, processStageId={}, timerType={}, teamType={}, duration={}秒",
                drillTaskId, processStageId, timerType, teamType, timerDuration);
    }

    @Override
    public void onTimerCompleted(Long processStageId, Long drillTaskId, SysUser user) {
        timeScoreToUser(processStageId, drillTaskId, user);
    }

    public void timeScoreToUser(Long processStageId, Long drillTaskId, SysUser user) {
        if (processStageId == null || drillTaskId == null) {
            return;
        }
        Integer count = drillProcessStageService.lambdaQuery()
                .eq(DrillProcessStage::getProcessStageId, processStageId)
                .eq(DrillProcessStage::getScoreType, ScoreTypeEnum.SCORE_SHOW.getCode())
                .count();
        if (count <= 0) {
            return;
        }
        DrillStageTimerScoreVO data = new DrillStageTimerScoreVO();
        data.setDrillTaskId(drillTaskId);
        data.setProcessStageId(processStageId);
        data.setScoreType(ScoreTypeEnum.SCORE_SHOW.getCode());

        buildPushScore(processStageId, drillTaskId, data);

        drillWebsocketService.timeScoreToUser(data, drillTaskId, user);
    }

    public void drillProcessEnd(DrillProcessVO drillProcessVO, SysUser user) {
        Long drillTaskId = drillProcessVO.getDrillTaskId();
        Date date = new Date();
        drillTaskService.lambdaUpdate()
                .set(DrillTask::getStatus, 3)
                .set(DrillTask::getDrillEndTime, date)
                .eq(DrillTask::getDrillTaskId, drillTaskId)
                .update();
        // 结束演练推送人全部去除
        drillProcessAllLeave(drillProcessVO);
    }

    public void buildAllScore(List<DrillTaskVO> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        List<Long> drillTaskIdList = CollStreamUtil.toList(records, DrillTaskVO::getDrillTaskId);
        if (CollUtil.isEmpty(drillTaskIdList)) {
            return;
        }

        List<DrillProcessStage> drillProcessStageList = drillProcessStageService.lambdaQuery()
                .in(DrillProcessStage::getDrillTaskId, drillTaskIdList)
                .eq(DrillProcessStage::getScoreType, ScoreTypeEnum.SCORE_SHOW.getCode())
                .list();

        if (CollUtil.isEmpty(drillProcessStageList)) {
            return;
        }

        List<Long> drillProcessStageIdList = CollStreamUtil.toList(drillProcessStageList, DrillProcessStage::getProcessStageId);
        if (CollUtil.isEmpty(drillProcessStageIdList)) {
            return;
        }

        List<DrillComment> drillCommentList = drillCommentService.lambdaQuery()
                .in(DrillComment::getProcessStageId, drillProcessStageIdList)
                .list();
        if (CollUtil.isEmpty(drillCommentList)) {
            return;
        }
        List<DrillCommentReply> drillCommentReplyList = drillCommentReplyService.lambdaQuery()
                .in(DrillCommentReply::getProcessStageId, drillProcessStageIdList)
                .eq(DrillCommentReply::getStatus, 0)
                .list();

        // 任务下所有comment都为需要显示分数的
        Map<Long, List<DrillComment>> drillTaskIdMap = CollStreamUtil.groupByKey(drillCommentList, DrillComment::getDrillTaskId);
        Map<Long, List<DrillCommentReply>> drillTaskIdReplyMap = CollStreamUtil.groupByKey(drillCommentReplyList, DrillCommentReply::getDrillTaskId);
        for (DrillTaskVO drillTaskVO : records) {
            Long drillTaskId = drillTaskVO.getDrillTaskId();
            if (!drillTaskIdMap.containsKey(drillTaskId)) {
                continue;
            }

            List<DrillComment> drillComments = drillTaskIdMap.get(drillTaskId);
            List<DrillCommentReply> drillCommentReplies = drillTaskIdReplyMap.get(drillTaskId);

            List<String> redBasiclScores = new ArrayList<>();
            List<String> blueBasicScores = new ArrayList<>();
            List<String> redExpertScores = new ArrayList<>();
            List<String> blueExpertScores = new ArrayList<>();

            drillScoreService.buildScore((red, blue) -> {
                redBasiclScores.add(red);
                blueBasicScores.add(blue);

            }, ScoreTypeEnum.SCORE_SHOW.getCode(), drillComments, drillCommentReplies);

            // 专家得分
            for (DrillComment drillComment : drillComments) {
                String commentType = drillComment.getCommentType();
                if (!Objects.equals(commentType, CommentEnum.EXPERT_COMMENT.getType())) {
                    continue;
                }
                Integer teamType = drillComment.getTeamType();
                String score = drillComment.getScore();
                // 专家点评分数
                if (Objects.equals(teamType, TeamTypeEnum.RED.getCode())) {
                    redExpertScores.add(score);
                } else if (Objects.equals(teamType, TeamTypeEnum.BLUE.getCode())) {
                    blueExpertScores.add(score);
                }
            }
            redBasiclScores.addAll(redExpertScores);
            blueBasicScores.addAll(blueExpertScores);

            String redBasicScoreStr = drillScoreService.getTotalScore(redBasiclScores);
            String blueBasicScoreStr = drillScoreService.getTotalScore(blueBasicScores);

            drillTaskVO.setRedStageScore(redBasicScoreStr);
            drillTaskVO.setBlueStageScore(blueBasicScoreStr);

            // 更新演练任务得分
            // List<String> redScore = new ArrayList<>();
            // List<String> blueScore = new ArrayList<>();
            // Map<Integer, List<DrillComment>> teamTypeMap = CollStreamUtil.groupByKey(drillComments, DrillComment::getTeamType);
            // teamTypeMap.forEach((teamType, v) -> {
            //     for (DrillComment drillComment : v) {
            //         String score = drillComment.getScore();
            //         if (Objects.equals(teamType, TeamTypeEnum.RED.getCode())) {
            //             redScore.add(score);
            //         } else if (Objects.equals(teamType, TeamTypeEnum.BLUE.getCode())) {
            //             blueScore.add(score);
            //         }
            //     }
            // });
            // String redTotalScore = drillScoreService.getTotalScore(redScore);
            // String blueTotalScore = drillScoreService.getTotalScore(blueScore);
            // drillTaskVO.setRedScore(redTotalScore);
            // drillTaskVO.setBlueScore(blueTotalScore);

        }

    }

    public void buildPushScore(Long processStageId, Long drillTaskId, DrillStageTimerScoreVO data) {
        List<DrillComment> processStageCommentList = drillCommentService.lambdaQuery()
                .eq(DrillComment::getProcessStageId, processStageId)
                .list();
        List<DrillCommentReply> drillCommentReplies = drillCommentReplyService.lambdaQuery()
                .eq(DrillCommentReply::getDrillTaskId, drillTaskId)
                .eq(DrillCommentReply::getProcessStageId, processStageId)
                .eq(DrillCommentReply::getStatus, 0)
                .list();

        drillScoreService.buildScore((red, blue) -> {
            data.setRedStageScore(red);
            data.setBlueStageScore(blue);
        }, ScoreTypeEnum.SCORE_SHOW.getCode(), processStageCommentList, drillCommentReplies);
    }

    public DrillCommentVO drillScoreQuery(DrillCommentVO drillCommentVO, SysUser user) {
        Long scoreProcessStageId = drillCommentVO.getScoreProcessStageId();
        Integer teamType = drillCommentVO.getTeamType();
        if (scoreProcessStageId == null) {
            throw new CustomException("processStageId 不能为空");
        }
        if (teamType == null) {
            throw new CustomException("teamType 不能为空");
        }
        Long userId = user.getUserId();

        List<DrillComment> drillComments = drillCommentService.lambdaQuery()
                .eq(DrillComment::getScoreProcessStageId, scoreProcessStageId)
                .eq(DrillComment::getCommentType, CommentEnum.EXPERT_COMMENT.getType())
                .eq(DrillComment::getTeamType, teamType)
                .eq(DrillComment::getUserId, userId)
                .list();
        if (CollUtil.isEmpty(drillComments)) {
            return null;
        }
        List<DrillCommentVO> drillCommentVOS = BeanUtil.copyToList(drillComments, DrillCommentVO.class);
        return drillCommentVOS.get(0);
    }

    public DrillStatisticsVO drillScoreStatistics(DrillCommentVO drillCommentVO, SysUser user) {
        Long scoreProcessStageId = drillCommentVO.getScoreProcessStageId();
        Integer teamType = drillCommentVO.getTeamType();
        if (scoreProcessStageId == null) {
            throw new CustomException("processStageId 不能为空");
        }
        if (teamType == null) {
            throw new CustomException("teamType 不能为空");
        }
        Long drillTaskId = drillCommentVO.getDrillTaskId();

        QueryWrapper<DrillComment> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("comment_type as commentType, count(*) as count");
        queryWrapper.groupBy("comment_type");
        queryWrapper.eq("process_stage_id", scoreProcessStageId);
        queryWrapper.eq("team_type", teamType);

        List<Map<String, Object>> maps = drillCommentService.getBaseMapper().selectMaps(queryWrapper);

        Map<String, Integer> longMap = maps.stream().collect(Collectors.toMap(
                map -> Convert.toStr(map.get("commentType")),
                map -> Convert.toInt(map.get("count"), 0)
        ));
        DrillStatisticsVO drillStatisticsVO = new DrillStatisticsVO();
        drillStatisticsVO.setDrillTaskId(drillTaskId);
        drillStatisticsVO.setScoreProcessStageId(scoreProcessStageId);
        drillStatisticsVO.setSituationCommentCount(longMap.getOrDefault(CommentEnum.SITUATION_COMMENT.getType(), 0));
        drillStatisticsVO.setPoliceCommentCount(longMap.getOrDefault(CommentEnum.POLICE_COMMENT.getType(), 0));
        drillStatisticsVO.setHotRankCount(longMap.getOrDefault(CommentEnum.HOT_RANK.getType(), 0));
        drillStatisticsVO.setHotTopicCount(longMap.getOrDefault(CommentEnum.HOT_TOPIC.getType(), 0));

        QueryWrapper<DrillCommentReply> wrapper = new QueryWrapper<>();
        wrapper.select("COUNT(*) AS totalCount", "SUM(like_count) AS totalLike")
                .eq("process_stage_id", scoreProcessStageId)
                .eq("team_type", teamType)
                .eq("status", 0);

        Map<String, Object> resultMap = drillCommentReplyService.getBaseMapper().selectMaps(wrapper).get(0);

        Integer totalCount = Convert.toInt(resultMap.get("totalCount"), 0);
        Integer totalLike = Convert.toInt(resultMap.get("totalLike"), 0);

        drillStatisticsVO.setReplyCount(totalCount);
        drillStatisticsVO.setLikeCount(totalLike);

        return drillStatisticsVO;
    }

    public List<DrillProcessStageDTO> drillStageQuery(DrillProcessStageDTO drillStageVO, SysUser user) {
        List<DrillProcessStageDTO> drillProcessStageDTOList;

        if (drillStageVO != null && drillStageVO.getDrillTaskId() != null) {
            Long drillTaskId = drillStageVO.getDrillTaskId();
            drillProcessStageDTOList = drillProcessStageService.getDrillProcessStageByDrillTaskId(drillTaskId, null);
        } else {
            List<DrillStage> drillStageList = drillStageService.lambdaQuery()
                    .eq(DrillStage::getDelFlag, 0)
                    .orderByAsc(DrillStage::getStageOrder)
                    .list();
            if (CollUtil.isEmpty(drillStageList)) {
                return Collections.emptyList();
            }
            drillProcessStageDTOList = BeanUtil.copyToList(drillStageList, DrillProcessStageDTO.class);
        }

        int virtualOrder = 1;
        for (DrillProcessStageDTO drillStage : drillProcessStageDTOList) {
            drillStage.setVirtualOrder(virtualOrder);
            virtualOrder++;
        }
        return drillProcessStageDTOList;
    }


    /**
     * 验证倒计时冲突
     */
    private void validateTimerConflicts(Long drillTaskId, Long processStageId, Integer timerType, Integer teamType) {
        // 查询当前阶段的所有倒计时记录
        List<DrillStageTime> stageTimers = drillStageTimeService.lambdaQuery()
                .eq(DrillStageTime::getDrillTaskId, drillTaskId)
                .eq(DrillStageTime::getProcessStageId, processStageId)
                .orderByDesc(DrillStageTime::getCreateTime)
                .list();

        if (CollUtil.isEmpty(stageTimers)) {
            return; // 没有冲突
        }

        // 检查是否有正在进行的倒计时
        for (DrillStageTime timer : stageTimers) {
            if (isTimerActive(timer)) {
                handleTimerConflict(timer, timerType, teamType);
            }
        }
    }

    /**
     * 检查倒计时是否正在进行
     */
    private boolean isTimerActive(DrillStageTime timer) {
        Long drillStageTimeId = timer.getDrillStageTimeId();
        String key = RedisConstant.DRILL_REMAIN_TIME_CACHE + drillStageTimeId;
        String remainTimerDurationStr = redisUtil.get(key);

        if (CharSequenceUtil.isNotBlank(remainTimerDurationStr)) {
            Integer remainingTime = Convert.toInt(remainTimerDurationStr);
            return remainingTime != null && remainingTime > 0;
        }

        return false;
    }

    /**
     * 处理倒计时冲突
     */
    private void handleTimerConflict(DrillStageTime existingTimer, Integer newTimerType, Integer newTeamType) {
        Integer existingTimerType = existingTimer.getTimerType();
        Integer existingTeamType = existingTimer.getTeamType();

        // 同时计时模式：不允许任何新的倒计时
        if (Objects.equals(existingTimerType, TimerTypeEnum.SAME_TIME.getCode())) {
            throw new CustomException("当前阶段正在进行同时计时，无法启动新的倒计时");
        }

        // 新启动的是同时计时：不允许与任何现有倒计时共存
        if (Objects.equals(newTimerType, TimerTypeEnum.SAME_TIME.getCode())) {
            throw new CustomException("当前阶段已有倒计时正在进行，无法启动同时计时模式");
        }

        // 分开计时模式：检查队伍冲突
        if (Objects.equals(existingTimerType, TimerTypeEnum.SEPARATELY_TIME.getCode()) &&
                Objects.equals(newTimerType, TimerTypeEnum.SEPARATELY_TIME.getCode())) {

            if (Objects.equals(existingTeamType, newTeamType)) {
                String teamName = getTeamName(newTeamType);
                throw new CustomException(teamName + "倒计时正在进行中，无法重复启动");
            }
            // 不同队伍可以同时计时
        }
    }

    /**
     * 获取队伍名称
     */
    private String getTeamName(Integer teamType) {
        if (Objects.equals(teamType, TeamTypeEnum.RED.getCode())) {
            return "红队";
        } else if (Objects.equals(teamType, TeamTypeEnum.BLUE.getCode())) {
            return "蓝队";
        }
        return "未知队伍";
    }


}
