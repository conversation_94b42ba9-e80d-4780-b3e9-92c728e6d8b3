package com.boryou.web.module.screen.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.web.module.screen.entity.CloudWord;
import com.boryou.web.module.screen.mapper.ScreenCloudWordMapper;
import com.boryou.web.module.screen.service.ScreenCloudWordService;
import com.boryou.web.module.screen.vo.CloudWordVO;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
@Service
public class ScreenCloudWordServiceImpl extends ServiceImpl<ScreenCloudWordMapper, CloudWord> implements ScreenCloudWordService {

    @Override
    public List<CloudWord> list(CloudWordVO cloudWord) {
        LambdaQueryWrapper<CloudWord> qw = new LambdaQueryWrapper<>();
        qw.ge(cloudWord.getStartTime() != null, CloudWord::getCreateTime, cloudWord.getStartTime())
        .le(cloudWord.getEndTime() != null, CloudWord::getCreateTime, cloudWord.getEndTime())
        .like(StrUtil.isNotEmpty(cloudWord.getWord()), CloudWord::getWord, cloudWord.getWord())
        .orderByDesc(CloudWord::getCreateTime);
        return baseMapper.selectList(qw);
    }



}
