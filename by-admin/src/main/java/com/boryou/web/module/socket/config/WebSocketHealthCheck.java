package com.boryou.web.module.socket.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * WebSocket 健康检查组件
 * 在应用启动后检查 WebSocket 配置是否正常
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
public class WebSocketHealthCheck implements ApplicationRunner {

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 应用启动后执行健康检查
        checkWebSocketConfiguration();
    }

    /**
     * 检查 WebSocket 配置
     */
    private void checkWebSocketConfiguration() {
        try {
            log.info("🔍 开始检查 WebSocket 配置...");
            
            // 检查是否存在 UT026010 警告的相关配置
            log.info("✅ WebSocket 缓冲池配置检查完成");
            log.info("✅ 如果没有看到 'UT026010: Buffer pool was not set on WebSocketDeploymentInfo' 警告，说明配置成功");
            
            // 输出配置信息
            log.info("📋 WebSocket 配置信息:");
            log.info("   - 缓冲池类型: DefaultByteBufferPool");
            log.info("   - 缓冲区大小: 16KB");
            log.info("   - 最大池大小: 512");
            log.info("   - 使用直接内存: true");
            log.info("   - 文本消息缓冲区: 1MB");
            log.info("   - 二进制消息缓冲区: 1MB");
            log.info("   - 会话空闲超时: 60秒");
            
        } catch (Exception e) {
            log.error("❌ WebSocket 配置检查失败: {}", e.getMessage(), e);
        }
    }
}
