package com.boryou.web.module.drill.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 演练用户注册VO类
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DrillUserRegisterVO {

    /**
     * 演练任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "演练任务ID不能为空")
    private Long drillTaskId;

    /**
     * 用户名（登录账号）
     */
    @NotBlank(message = "用户名不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9_]{4,20}$", message = "用户名只能包含字母、数字、下划线，长度4-20位")
    private String userName;

    /**
     * 用户昵称（显示名称）
     */
    @NotBlank(message = "用户昵称不能为空")
    private String nickName;

    /**
     * 用户密码
     */
    @NotBlank(message = "用户密码不能为空")
    @Pattern(regexp = "^.{6,20}$", message = "密码长度必须在6-20位之间")
    private String password;

    /**
     * 手机号码（可选）
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号码格式不正确")
    private String phonenumber;

    /**
     * 演练角色类型
     * red: 红队
     * blue: 蓝队  
     * spectator: 游客
     */
    @NotBlank(message = "演练角色不能为空")
    @Pattern(regexp = "^(red|blue|spectator)$", message = "演练角色只能是red、blue、spectator之一")
    private String drillRole;

    /**
     * 部门ID（从演练任务中获取）
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long deptId;
}
