package com.boryou.web.module.drill.util;

import com.boryou.web.constant.RedisConstant;
import com.boryou.web.util.RedisStaticUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 点赞Redis工具类
 */
@Slf4j
@Component
public class LikeRedisUtil {

    // 过期时间设置为7天
    public static final long EXPIRE_TIME = 7;
    public static final TimeUnit EXPIRE_TIME_UNIT = TimeUnit.DAYS;

    /**
     * 获取点赞状态的Redis键
     */
    public String getLikeStatusKey(Long userId, Long commentReplyId, String commentType) {
        return RedisConstant.DRILL_LIKE_STATUS_PREFIX + userId + ":" + commentReplyId + ":" + commentType;
    }

    /**
     * 获取点赞计数的Redis键
     */
    public String getLikeCountKey(Long commentReplyId, String commentType) {
        return RedisConstant.DRILL_LIKE_COUNT_PREFIX + commentReplyId + ":" + commentType;
    }

    /**
     * 设置点赞状态
     */
    public void setLikeStatus(Long userId, Long commentReplyId, String commentType, boolean isLiked) {
        String key = getLikeStatusKey(userId, commentReplyId, commentType);
        RedisStaticUtils.set(key, isLiked ? "1" : "0");
        RedisStaticUtils.expire(key, EXPIRE_TIME, EXPIRE_TIME_UNIT);
    }

    /**
     * 获取点赞状态
     */
    public Boolean getLikeStatus(Long userId, Long commentReplyId, String commentType) {
        String key = getLikeStatusKey(userId, commentReplyId, commentType);
        String value = RedisStaticUtils.getStr(key);
        return "1".equals(value);
    }

    /**
     * 批量获取点赞状态
     */
    public Map<Long, Boolean> batchGetLikeStatus(Long userId, List<Long> commentReplyIds, String commentType) {
        Map<Long, Boolean> result = new HashMap<>(commentReplyIds.size());

        // 构建所有键
        List<String> keys = new ArrayList<>(commentReplyIds.size());
        Map<String, Long> keyToIdMap = new HashMap<>(commentReplyIds.size());
        for (Long replyId : commentReplyIds) {
            String key = getLikeStatusKey(userId, replyId, commentType);
            keys.add(key);
            keyToIdMap.put(key, replyId);
        }

        // 批量获取
        List<String> values = RedisStaticUtils.mget(keys, String.class);
        if (values != null) {
            for (int i = 0; i < keys.size() && i < values.size(); i++) {
                String key = keys.get(i);
                String value = values.get(i);
                Long replyId = keyToIdMap.get(key);
                result.put(replyId, "1".equals(value));
            }
        }

        return result;
    }

    /**
     * 更新点赞计数
     */
    public Long updateLikeCount(Long commentReplyId, String commentType, int delta) {
        String key = getLikeCountKey(commentReplyId, commentType);

        // 获取当前值
        String currentValue = RedisStaticUtils.getStr(key);
        long currentCount = currentValue != null ? Long.parseLong(currentValue) : 0;
        long newCount = currentCount + delta;

        // 确保计数不为负数
        if (newCount < 0) {
            newCount = 0;
        }

        // 设置新值并设置过期时间
        RedisStaticUtils.set(key, String.valueOf(newCount), EXPIRE_TIME, EXPIRE_TIME_UNIT);

        return newCount;
    }

    /**
     * 获取点赞计数
     */
    public Long getLikeCount(Long commentReplyId, String commentType) {
        String key = getLikeCountKey(commentReplyId, commentType);
        String value = RedisStaticUtils.getStr(key);
        return value != null ? Long.parseLong(value) : 0L;
    }

    /**
     * 检查Redis中的点赞数据是否存在
     *
     * @return 是否需要从数据库恢复数据
     */
    public boolean needRecoverLikeData(Long userId, Long commentReplyId, String commentType) {
        // 获取Redis键
        String statusKey = getLikeStatusKey(userId, commentReplyId, commentType);
        String countKey = getLikeCountKey(commentReplyId, commentType);

        // 检查Redis中是否存在点赞状态和点赞计数
        boolean statusExists = RedisStaticUtils.hasKey(statusKey);
        boolean countExists = RedisStaticUtils.hasKey(countKey);

        // 如果Redis中数据不完整，需要恢复
        return !statusExists || !countExists;
    }

    /**
     * 恢复Redis中的点赞数据
     *
     * @param userId              用户ID
     * @param commentReplyId      评论回复ID
     * @param commentType         评论类型
     * @param getLikeStatusFromDB 从数据库获取点赞状态的回调函数
     * @param getLikeCountFromDB  从数据库获取点赞数量的回调函数
     * @return 是否成功恢复数据
     */
    public boolean recoverLikeData(Long userId, Long commentReplyId, String commentType,
                                   Supplier<Boolean> getLikeStatusFromDB,
                                   Supplier<Long> getLikeCountFromDB) {
        try {
            log.info("点赞数据在Redis中不存在，从数据库恢复。userId={}, commentReplyId={}, commentType={}",
                    userId, commentReplyId, commentType);

            // 从数据库获取点赞状态
            boolean isLikedInDB = getLikeStatusFromDB.get();

            // 将数据库中的点赞状态缓存到Redis
            setLikeStatus(userId, commentReplyId, commentType, isLikedInDB);

            // 从数据库获取点赞数量
            long likeCountInDB = getLikeCountFromDB.get();

            // 更新Redis中的点赞计数
            RedisStaticUtils.set(
                    getLikeCountKey(commentReplyId, commentType),
                    String.valueOf(likeCountInDB),
                    EXPIRE_TIME,
                    EXPIRE_TIME_UNIT
            );

            log.info("从数据库恢复点赞数据到Redis成功。isLiked={}, likeCount={}", isLikedInDB, likeCountInDB);
            return true;
        } catch (Exception e) {
            log.error("从数据库恢复点赞数据失败", e);
            return false;
        }
    }

    /**
     * 批量检查Redis中的点赞数据是否需要恢复
     *
     * @param userId          用户ID
     * @param commentReplyIds 评论回复ID列表
     * @param commentType     评论类型
     * @return 需要恢复的评论回复ID列表
     */
    public List<Long> batchNeedRecoverLikeData(Long userId, List<Long> commentReplyIds, String commentType) {
        if (commentReplyIds.isEmpty()) {
            return new ArrayList<>();
        }

        List<Long> needRecoverIds = new ArrayList<>();

        // 构建所有状态键和计数键
        List<String> statusKeys = new ArrayList<>(commentReplyIds.size());
        List<String> countKeys = new ArrayList<>(commentReplyIds.size());

        for (Long replyId : commentReplyIds) {
            statusKeys.add(getLikeStatusKey(userId, replyId, commentType));
            countKeys.add(getLikeCountKey(replyId, commentType));
        }

        // 批量检查状态键是否存在
        List<Boolean> statusExists = batchHasKey(statusKeys);
        List<Boolean> countExists = batchHasKey(countKeys);

        // 检查哪些需要恢复
        for (int i = 0; i < commentReplyIds.size(); i++) {
            boolean statusExist = i < statusExists.size() && Boolean.TRUE.equals(statusExists.get(i));
            boolean countExist = i < countExists.size() && Boolean.TRUE.equals(countExists.get(i));

            // 如果Redis中数据不完整，需要恢复
            if (!statusExist || !countExist) {
                needRecoverIds.add(commentReplyIds.get(i));
            }
        }

        return needRecoverIds;
    }

    /**
     * 批量检查键是否存在
     */
    private List<Boolean> batchHasKey(List<String> keys) {
        List<Boolean> results = new ArrayList<>(keys.size());

        // 由于Redis没有批量hasKey操作，我们使用mget来检查
        // 如果值为null，说明键不存在
        List<String> values = RedisStaticUtils.mget(keys, String.class);

        for (int i = 0; i < keys.size(); i++) {
            boolean exists = i < values.size() && values.get(i) != null;
            results.add(exists);
        }

        return results;
    }

    /**
     * 批量获取点赞计数
     */
    public Map<Long, Long> batchGetLikeCount(List<Long> commentReplyIds, String commentType) {
        Map<Long, Long> result = new HashMap<>(commentReplyIds.size());

        if (commentReplyIds.isEmpty()) {
            return result;
        }

        // 构建所有键
        List<String> keys = new ArrayList<>(commentReplyIds.size());
        Map<String, Long> keyToIdMap = new HashMap<>(commentReplyIds.size());

        for (Long replyId : commentReplyIds) {
            String key = getLikeCountKey(replyId, commentType);
            keys.add(key);
            keyToIdMap.put(key, replyId);
        }

        // 批量获取
        List<String> values = RedisStaticUtils.mget(keys, String.class);
        if (values != null) {
            for (int i = 0; i < keys.size() && i < values.size(); i++) {
                String key = keys.get(i);
                String value = values.get(i);
                Long replyId = keyToIdMap.get(key);
                Long count = value != null ? Long.parseLong(value) : 0L;
                result.put(replyId, count);
            }
        }

        return result;
    }

    /**
     * 批量获取多种评论类型的点赞状态
     *
     * @param userId 用户ID
     * @param commentTypeToIdsMap 评论类型到评论ID列表的映射
     * @return 评论ID到点赞状态的映射
     */
    public Map<Long, Boolean> batchGetLikeStatusForMultipleTypes(Long userId, Map<String, List<Long>> commentTypeToIdsMap) {
        Map<Long, Boolean> result = new HashMap<>();

        commentTypeToIdsMap.forEach((commentType, commentReplyIds) -> {
            if (!commentReplyIds.isEmpty()) {
                Map<Long, Boolean> typeResult = batchGetLikeStatus(userId, commentReplyIds, commentType);
                result.putAll(typeResult);
            }
        });

        return result;
    }

    /**
     * 切换点赞状态
     *
     * @param userId              用户ID
     * @param commentReplyId      评论回复ID
     * @param commentType         评论类型
     * @param getLikeStatusFromDB 从数据库获取点赞状态的回调函数
     * @param getLikeCountFromDB  从数据库获取点赞数量的回调函数
     * @return 返回新的点赞状态和点赞数
     */
    public Map<String, Object> toggleLikeStatus(Long userId, Long commentReplyId, String commentType,
                                                Supplier<Boolean> getLikeStatusFromDB,
                                                Supplier<Long> getLikeCountFromDB) {
        Map<String, Object> result = new HashMap<>(3);

        // 检查Redis中的点赞数据是否需要恢复
        boolean needRecover = needRecoverLikeData(userId, commentReplyId, commentType);
        boolean recoveredFromDB = false;

        // 如果需要恢复，则从数据库恢复数据
        if (needRecover) {
            recoveredFromDB = recoverLikeData(userId, commentReplyId, commentType, getLikeStatusFromDB, getLikeCountFromDB);
        }

        // 获取当前点赞状态
        Boolean currentStatus = getLikeStatus(userId, commentReplyId, commentType);
        boolean newStatus = !currentStatus;

        // 更新点赞状态
        setLikeStatus(userId, commentReplyId, commentType, newStatus);

        // 更新点赞计数
        int delta = newStatus ? 1 : -1;
        Long newCount = updateLikeCount(commentReplyId, commentType, delta);

        result.put("isLiked", newStatus);
        result.put("likeCount", newCount);
        result.put("recoveredFromDB", recoveredFromDB);

        return result;
    }
}
