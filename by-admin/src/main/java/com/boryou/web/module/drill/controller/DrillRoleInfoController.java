package com.boryou.web.module.drill.controller;

import com.boryou.common.core.domain.AjaxResult;
import com.boryou.web.module.drill.enums.DrillRoleEnum;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 演练角色信息控制器
 * 提供前端选择角色时需要的数据
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/drill/role")
@Api(tags = "演练角色信息")
public class DrillRoleInfoController {

    /**
     * 获取可选择的演练角色列表
     * 
     * @return 角色列表
     */
    @GetMapping("/options")
    @ApiOperation("获取演练角色选项")
    public AjaxResult getRoleOptions() {
        List<Map<String, Object>> roleOptions = new ArrayList<>();
        
        for (DrillRoleEnum roleEnum : DrillRoleEnum.values()) {
            Map<String, Object> option = new HashMap<>();
            option.put("code", roleEnum.getCode());
            option.put("name", roleEnum.getName());
            option.put("description", roleEnum.getDescription());
            roleOptions.add(option);
        }
        
        return AjaxResult.success(roleOptions);
    }
}
