package com.boryou.web.module.drill.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.boryou.common.core.domain.entity.SysRole;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.system.service.ISysRoleService;
import com.boryou.system.service.ISysUserService;
import com.boryou.web.module.drill.domain.DrillTask;
import com.boryou.web.module.drill.domain.vo.DrillUserRegisterRes;
import com.boryou.web.module.drill.domain.vo.DrillUserRegisterVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 演练用户注册服务类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DrillUserRegisterService {

    private final ISysUserService userService;
    private final ISysRoleService roleService;
    private final DrillTaskService drillTaskService;

    /**
     * 用户注册
     * 
     * @param registerVO 注册信息
     * @return 注册结果
     */
    @Transactional(rollbackFor = Exception.class)
    public DrillUserRegisterRes registerUser(DrillUserRegisterVO registerVO) {
        // 1. 参数验证
        validateRegisterInfo(registerVO);
        
        // 2. 获取演练任务信息
        DrillTask drillTask = drillTaskService.getDrillTaskByIdWithThrow(registerVO.getDrillTaskId());
        
        // 3. 创建系统用户
        SysUser sysUser = createSysUser(registerVO, drillTask);
        
        // 4. 分配基础角色（普通用户角色）
        assignBasicRole(sysUser);
        
        // 5. 保存用户
        userService.insertUser(sysUser);
        
        // 6. 根据演练角色加入对应队伍
        boolean joinedTeam = joinDrillTeam(sysUser, drillTask, registerVO.getDrillRole());
        
        // 7. 构建返回结果
        return buildRegisterResult(sysUser, drillTask, registerVO.getDrillRole(), joinedTeam);
    }

    /**
     * 验证注册信息
     */
    private void validateRegisterInfo(DrillUserRegisterVO registerVO) {
        // 检查用户名是否已存在
        SysUser existUser = userService.selectUserByUserName(registerVO.getUserName());
        if (existUser != null) {
            throw new CustomException("用户名已存在，请选择其他用户名");
        }
        
        // 检查手机号是否已存在（如果提供了手机号）
        if (CharSequenceUtil.isNotBlank(registerVO.getPhonenumber())) {
            SysUser phoneUser = userService.getUserByTelephone(registerVO.getPhonenumber());
            if (phoneUser != null) {
                throw new CustomException("手机号已存在，请使用其他手机号");
            }
        }
    }

    /**
     * 创建系统用户对象
     */
    private SysUser createSysUser(DrillUserRegisterVO registerVO, DrillTask drillTask) {
        SysUser sysUser = new SysUser();
        sysUser.setUserName(registerVO.getUserName());
        sysUser.setNickName(registerVO.getNickName());
        sysUser.setPassword(SecurityUtils.encryptPassword(registerVO.getPassword()));
        sysUser.setPhonenumber(registerVO.getPhonenumber());
        sysUser.setDeptId(drillTask.getDeptId());
        sysUser.setStatus("0"); // 正常状态
        sysUser.setDelFlag("0"); // 未删除
        sysUser.setUserType("00"); // 默认用户类型
        sysUser.setCreateTime(new Date());
        sysUser.setCreateBy("drill_register"); // 标识为演练注册用户
        
        return sysUser;
    }

    /**
     * 分配基础角色（普通用户角色）
     */
    private void assignBasicRole(SysUser sysUser) {
        // 查找普通用户角色（假设角色key为"common"或类似）
        LambdaQueryWrapper<SysRole> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysRole::getStatus, "0")
               .eq(SysRole::getDelFlag, "0")
               .and(w -> w.eq(SysRole::getRoleKey, "common")
                        .or()
                        .eq(SysRole::getRoleKey, "user")
                        .or()
                        .like(SysRole::getRoleName, "普通用户"));
        
        List<SysRole> roles = roleService.list(wrapper);
        if (CollUtil.isNotEmpty(roles)) {
            // 取第一个匹配的角色
            SysRole role = roles.get(0);
            sysUser.setRoleIds(new Long[]{role.getRoleId()});
            log.info("为用户 {} 分配基础角色: {}", sysUser.getUserName(), role.getRoleName());
        } else {
            log.warn("未找到合适的基础角色，用户 {} 将不分配角色", sysUser.getUserName());
            sysUser.setRoleIds(new Long[0]);
        }
    }

    /**
     * 根据演练角色加入对应队伍
     */
    private boolean joinDrillTeam(SysUser sysUser, DrillTask drillTask, String drillRole) {
        String userIdStr = String.valueOf(sysUser.getUserId());
        boolean joined = false;
        
        try {
            switch (drillRole) {
                case "red":
                    // 加入红队
                    joined = addToRedTeam(drillTask, userIdStr);
                    log.info("用户 {} 申请加入红队，结果: {}", sysUser.getUserName(), joined ? "成功" : "失败");
                    break;
                case "blue":
                    // 加入蓝队
                    joined = addToBlueTeam(drillTask, userIdStr);
                    log.info("用户 {} 申请加入蓝队，结果: {}", sysUser.getUserName(), joined ? "成功" : "失败");
                    break;
                case "spectator":
                    // 游客不需要加入队伍，直接返回true
                    joined = true;
                    log.info("用户 {} 注册为游客角色", sysUser.getUserName());
                    break;
                default:
                    log.warn("未知的演练角色: {}", drillRole);
                    break;
            }
        } catch (Exception e) {
            log.error("用户 {} 加入演练队伍失败: {}", sysUser.getUserName(), e.getMessage(), e);
            joined = false;
        }
        
        return joined;
    }

    /**
     * 加入红队
     */
    private boolean addToRedTeam(DrillTask drillTask, String userIdStr) {
        List<String> redMembers = drillTask.getRedMember();
        if (redMembers == null) {
            redMembers = new ArrayList<>();
        }
        
        // 检查是否已经在红队中
        if (redMembers.contains(userIdStr)) {
            return true;
        }
        
        // 添加到红队成员列表
        redMembers.add(userIdStr);
        drillTask.setRedMember(redMembers);
        
        // 更新数据库
        return drillTaskService.updateById(drillTask);
    }

    /**
     * 加入蓝队
     */
    private boolean addToBlueTeam(DrillTask drillTask, String userIdStr) {
        List<String> blueMembers = drillTask.getBlueMember();
        if (blueMembers == null) {
            blueMembers = new ArrayList<>();
        }
        
        // 检查是否已经在蓝队中
        if (blueMembers.contains(userIdStr)) {
            return true;
        }
        
        // 添加到蓝队成员列表
        blueMembers.add(userIdStr);
        drillTask.setBlueMember(blueMembers);
        
        // 更新数据库
        return drillTaskService.updateById(drillTask);
    }

    /**
     * 构建注册结果
     */
    private DrillUserRegisterRes buildRegisterResult(SysUser sysUser, DrillTask drillTask, 
                                                   String drillRole, boolean joinedTeam) {
        String roleName;
        String message;
        
        switch (drillRole) {
            case "red":
                roleName = "红队成员";
                message = joinedTeam ? "注册成功，已加入红队！" : "注册成功，但加入红队失败，请联系管理员";
                break;
            case "blue":
                roleName = "蓝队成员";
                message = joinedTeam ? "注册成功，已加入蓝队！" : "注册成功，但加入蓝队失败，请联系管理员";
                break;
            case "spectator":
                roleName = "游客";
                message = "注册成功，您可以作为游客观看演练！";
                break;
            default:
                roleName = "未知角色";
                message = "注册成功，但角色分配异常";
                break;
        }
        
        return DrillUserRegisterRes.builder()
                .userId(sysUser.getUserId())
                .userName(sysUser.getUserName())
                .nickName(sysUser.getNickName())
                .drillTaskId(drillTask.getDrillTaskId())
                .drillRole(drillRole)
                .drillRoleName(roleName)
                .joinedTeam(joinedTeam)
                .message(message)
                .build();
    }
}
