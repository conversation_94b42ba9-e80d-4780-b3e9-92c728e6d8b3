package com.boryou.web.module.drill.controller;

import com.boryou.common.core.domain.AjaxResult;
import com.boryou.web.module.drill.domain.DrillTask;
import com.boryou.web.module.drill.domain.vo.DrillUserRegisterVO;
import com.boryou.web.module.drill.service.DrillTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 演练用户注册测试控制器
 * 用于测试和调试注册功能
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/drill/user/test")
@Api(tags = "演练用户注册测试")
public class DrillUserRegisterTestController {

    private final DrillTaskService drillTaskService;

    /**
     * 获取演练任务信息（用于测试）
     * 
     * @param drillTaskId 演练任务ID
     * @return 演练任务信息
     */
    @GetMapping("/task/{drillTaskId}")
    @ApiOperation("获取演练任务信息")
    public AjaxResult getDrillTaskInfo(@PathVariable Long drillTaskId) {
        try {
            DrillTask drillTask = drillTaskService.getDrillTaskByIdWithThrow(drillTaskId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("drillTaskId", drillTask.getDrillTaskId());
            result.put("taskTitle", drillTask.getTaskTitle());
            result.put("taskContent", drillTask.getTaskContent());
            result.put("deptId", drillTask.getDeptId());
            result.put("status", drillTask.getStatus());
            result.put("redCaptain", drillTask.getRedCaptain());
            result.put("redMember", drillTask.getRedMember());
            result.put("blueCaptain", drillTask.getBlueCaptain());
            result.put("blueMember", drillTask.getBlueMember());
            
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("获取演练任务信息失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取演练任务信息失败: " + e.getMessage());
        }
    }

    /**
     * 生成测试注册数据
     * 
     * @param drillTaskId 演练任务ID
     * @return 测试注册数据
     */
    @GetMapping("/generate-test-data/{drillTaskId}")
    @ApiOperation("生成测试注册数据")
    public AjaxResult generateTestData(@PathVariable Long drillTaskId) {
        try {
            DrillTask drillTask = drillTaskService.getDrillTaskByIdWithThrow(drillTaskId);
            
            // 生成红队测试数据
            DrillUserRegisterVO redTeamUser = DrillUserRegisterVO.builder()
                    .drillTaskId(drillTaskId)
                    .userName("test_red_" + System.currentTimeMillis())
                    .nickName("红队测试用户")
                    .password("123456")
                    .phonenumber("13800138001")
                    .drillRole("red")
                    .deptId(drillTask.getDeptId())
                    .build();
            
            // 生成蓝队测试数据
            DrillUserRegisterVO blueTeamUser = DrillUserRegisterVO.builder()
                    .drillTaskId(drillTaskId)
                    .userName("test_blue_" + System.currentTimeMillis())
                    .nickName("蓝队测试用户")
                    .password("123456")
                    .phonenumber("13800138002")
                    .drillRole("blue")
                    .deptId(drillTask.getDeptId())
                    .build();
            
            // 生成游客测试数据
            DrillUserRegisterVO spectatorUser = DrillUserRegisterVO.builder()
                    .drillTaskId(drillTaskId)
                    .userName("test_spectator_" + System.currentTimeMillis())
                    .nickName("游客测试用户")
                    .password("123456")
                    .phonenumber("13800138003")
                    .drillRole("spectator")
                    .deptId(drillTask.getDeptId())
                    .build();
            
            Map<String, Object> result = new HashMap<>();
            result.put("redTeamUser", redTeamUser);
            result.put("blueTeamUser", blueTeamUser);
            result.put("spectatorUser", spectatorUser);
            result.put("message", "测试数据生成成功，可以使用这些数据进行注册测试");
            
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("生成测试数据失败: {}", e.getMessage(), e);
            return AjaxResult.error("生成测试数据失败: " + e.getMessage());
        }
    }

    /**
     * 检查演练任务队伍状态
     * 
     * @param drillTaskId 演练任务ID
     * @return 队伍状态信息
     */
    @GetMapping("/team-status/{drillTaskId}")
    @ApiOperation("检查演练任务队伍状态")
    public AjaxResult getTeamStatus(@PathVariable Long drillTaskId) {
        try {
            DrillTask drillTask = drillTaskService.getDrillTaskByIdWithThrow(drillTaskId);
            
            List<String> redMembers = drillTask.getRedMember();
            List<String> blueMembers = drillTask.getBlueMember();
            
            Map<String, Object> result = new HashMap<>();
            result.put("drillTaskId", drillTaskId);
            result.put("taskTitle", drillTask.getTaskTitle());
            result.put("redCaptain", drillTask.getRedCaptain());
            result.put("redMemberCount", redMembers != null ? redMembers.size() : 0);
            result.put("redMembers", redMembers);
            result.put("blueCaptain", drillTask.getBlueCaptain());
            result.put("blueMemberCount", blueMembers != null ? blueMembers.size() : 0);
            result.put("blueMembers", blueMembers);
            
            return AjaxResult.success(result);
        } catch (Exception e) {
            log.error("获取队伍状态失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取队伍状态失败: " + e.getMessage());
        }
    }
}
