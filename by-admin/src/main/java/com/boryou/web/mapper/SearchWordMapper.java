package com.boryou.web.mapper;

import com.boryou.web.domain.SearchWord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【by_search_word】的数据库操作Mapper
 * @createDate 2024-05-22 19:55:31
 * @Entity com.boryou.web.domain.SearchWord
 */
public interface SearchWordMapper {

    int deleteByPrimaryKey(Long id);

    int insert(SearchWord record);

    int insertSelective(SearchWord record);

    SearchWord selectByPrimaryKey(Long id);

    int updateByPrimaryKey(SearchWord record);

    List<SearchWord> selectHotWordByUserIdCount(@Param("userId") Long userId, @Param("count") Integer count);

    List<SearchWord> selectHotWordByUserIdsCount(@Param("deptIds") List<String> deptIds, @Param("count") Integer count);

    int addWord(SearchWord searchWord);

    SearchWord selectHotWordByUserIdWord(@Param("userId") Long userId, @Param("word") String word);

    int updateWordById(SearchWord searchWord);

    int deleteWord(@Param("word") String word, @Param("userId") Long userId);
}
