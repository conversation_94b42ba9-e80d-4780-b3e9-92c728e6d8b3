package com.boryou.web.service;

import cn.hutool.core.collection.CollUtil;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.web.domain.SearchWord;
import com.boryou.web.domain.vo.RecommendVO;
import com.boryou.web.mapper.SearchWordMapper;
import com.boryou.web.module.drill.service.DrillTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RecommendService {

    @Resource
    private SearchWordMapper searchWordMapper;

    @Resource
    private DrillTaskService drillTaskService;

    public List<String> selectRecommendWord(RecommendVO recommendVO, SysUser user) {
        if (user == null) {
            return CollUtil.newArrayList();
        }
        Integer count = recommendVO.getCount();
        if (count == null || count <= 0) {
            count = 5;
        }
        if (count > 10) {
            count = 10;
        }
        List<String> allDeptIdWithThrow = drillTaskService.getAllDeptIdWithThrow(user);
        List<SearchWord> searchWords = searchWordMapper.selectHotWordByUserIdsCount(allDeptIdWithThrow, count);
        return searchWords.stream().map(SearchWord::getWord).collect(Collectors.toList());
    }


}
