package com.boryou.web.task;

import com.boryou.common.constant.Constants;
import com.boryou.web.service.PlanService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * 历史方案-存储es逻辑
 *
 * <AUTHOR>
 * @date 2024-06-21 10:29
 */
@EnableScheduling
@Slf4j
@Configuration
@RequiredArgsConstructor
public class HistoryPlanTask extends DistributedTask {
    @Resource
    private PlanService planService;

    private final String LOCK_KEY = Constants.PROJECT_PREFIX + "scanPlanTaskLock";

//    @PreDestroy
//    public void destroy(){
//        log.info("释放历史方案定时任务锁key");
//        unlock(LOCK_KEY);
//    }

    // @Scheduled(fixedDelay = 1 * 60 * 1000)
    public void scanPlanTask() {
//        long start = System.currentTimeMillis();
//        log.info("开始扫描方案");
        if (!tryLock(LOCK_KEY, TimeUnit.MILLISECONDS, 10)) {
            log.info("历史方案定时任务已被其他实例占用，本次跳过执行。");
            return;
        }
        try {
            planService.scanPlan();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            unlock(LOCK_KEY);
        }
//        log.info("开始扫描方案结束：本次耗时：{}毫秒", ((System.currentTimeMillis() - start) / 1000));
    }

}

