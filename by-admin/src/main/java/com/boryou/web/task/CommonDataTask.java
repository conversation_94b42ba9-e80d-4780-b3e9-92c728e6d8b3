package com.boryou.web.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.aggregations.*;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boryou.common.utils.RedisUtil;
import com.boryou.web.constant.ElasticsearchIndex;
import com.boryou.web.constant.RedisConstant;
import com.boryou.web.controller.common.enums.MediaTypeEnum;
import com.boryou.web.domain.AccountInfo;
import com.boryou.web.domain.vo.AccountInfoVO;
import com.boryou.web.module.area.entity.Area;
import com.boryou.web.module.home.service.HomeStatisService;
import com.boryou.web.service.AccountInfoService;
import com.boryou.web.service.ElasticsearchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-07-18 15:59
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class CommonDataTask extends DistributedTask {

    private final HomeStatisService homeStatisService;
    public static Map<Long, List<Area>> COURT_MAPS = new HashMap<>(200);
    public static Map<String, Long> COURT_NAME_CODE_MAPS = new HashMap<>(200);
    private final AccountInfoService accountInfoService;
    private final ElasticsearchClient esClient;
    private final RedisUtil redisUtil;
    private final ElasticsearchService elasticsearchService;
    public static final String ACCOUNT_INFO_CACHE_LOCK = RedisConstant.system_prefix + "account_info_cache_lock";
    /**
     * 信源host唯一的媒体类型
     */
    public static final List<MediaTypeEnum> UNIQUE_HOST_MEDIA_TYPE_LIST = Arrays.asList(MediaTypeEnum.NEWS, MediaTypeEnum.AUDIO, MediaTypeEnum.TV, MediaTypeEnum.GOV, MediaTypeEnum.COMMENT);

    /**
     * 获取法院的配置数据 ，实现动态配置的效果
     *
     * <AUTHOR>
     * @date 2024/7/18 17:09
     **/
    @PostConstruct
    public void getAllArea() {
        List<Area> allArea = homeStatisService.getAllArea();
        for (Area area : allArea) {
            COURT_NAME_CODE_MAPS.put(area.getAreaName(), Long.parseLong(area.getCode()));//缓存全量的法院对应的code
        }
        List<Area> cityCourtList = allArea.stream().filter(s -> s.getLevel() == 1).collect(Collectors.toList());
        for (Area area : cityCourtList) {
            List<Area> collect1 = allArea.stream().filter(s -> s.getParentId() == Integer.parseInt(area.getCode())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect1)) {
                collect1.add(area);//每级的法院，需要把父级也加进去
                COURT_MAPS.put(Long.parseLong(area.getCode()), collect1);//遍历第一级
            }
            for (Area area1 : collect1) {
                List<Area> collect3 = allArea.stream().filter(s -> s.getParentId() == Integer.parseInt(area1.getCode())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(collect3)) {
                    collect3.add(area1);//每级的法院，需要把父级也加进去
                    COURT_MAPS.put(Long.parseLong(area1.getCode()), collect3);//遍历第二级
                }
            }
        }
    }


    /**
     * 同步并缓存accountInfo信息
     * 八小时一次
     */
//    @PostConstruct
//     @Scheduled(fixedDelay = 8 * 60 * 60 * 1000)
    public void syncAccountInfo() {
        //根据媒体类型查询出信源进行缓存
        QueryWrapper<AccountInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("MAX(update_time) as update_time");
        AccountInfo one = accountInfoService.getOne(queryWrapper);
        String aggKey = "hostAgg";
        Date date = new Date();
        for (MediaTypeEnum value : MediaTypeEnum.values()) {
            if (!UNIQUE_HOST_MEDIA_TYPE_LIST.contains(value)) {
                BoolQuery.Builder bool = QueryBuilders.bool();
                Query termQuery = QueryBuilders.term(t -> t.field("type").value(value.getValue()));
                bool.must(termQuery);
                //若存在同步记录,则只统计上次同步之后的数据
                if (one != null) {
                    Date updateTime = one.getUpdateTime();
                    Query rangeQuery = QueryBuilders.range(r -> r.field("updateTime").from(DateUtil.format(updateTime, DatePattern.NORM_DATETIME_PATTERN)));
                    bool.must(rangeQuery);
                }
                //不包含才进行缓存
                Aggregation aggregation = AggregationBuilders.terms(t -> t.field("domain").size(50000));
                SearchRequest searchRequest = new SearchRequest.Builder()
                        .index(ElasticsearchIndex.NETXMAN_ACCOUNT_INFO)
                        .query(bool.build()._toQuery())
                        .aggregations(aggKey, aggregation)
                        .size(0)
                        .build();

                try {
                    SearchResponse<Long> response = esClient.search(searchRequest, Long.class);
                    if (response != null) {
                        Map<String, Aggregate> aggregations = response.aggregations();
                        Aggregate aggregate = aggregations.get(aggKey);
                        StringTermsAggregate stringTermsAggregate = (StringTermsAggregate) aggregate._get();
                        List<StringTermsBucket> array = stringTermsAggregate.buckets().array();
                        List<String> hostList = new ArrayList<>();
                        for (StringTermsBucket stringTermsBucket : array) {
                            String host = stringTermsBucket.key().stringValue();
                            hostList.add(host);
                        }
                        List<AccountInfoVO> accountInfoVOList = elasticsearchService.getNameByHostsFromEs(hostList, value.getValue());
                        List<AccountInfo> accountInfos = new ArrayList<>();
                        for (AccountInfoVO accountInfoVO : accountInfoVOList) {
                            String redisKey = concatAccountInfoKey(String.valueOf(value.getValue()), accountInfoVO.getDomain());
                            String str = redisUtil.get(redisKey);
                            if (CharSequenceUtil.isEmpty(str)) {
                                LambdaQueryWrapper<AccountInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                                lambdaQueryWrapper.eq(AccountInfo::getHost, accountInfoVO.getDomain()).eq(AccountInfo::getType, accountInfoVO.getType());
                                AccountInfo one1 = accountInfoService.getOne(lambdaQueryWrapper);
                                if (one1 == null) {
                                    if (CharSequenceUtil.isNotEmpty(accountInfoVO.getDomain()) && CharSequenceUtil.isNotEmpty(accountInfoVO.getSector())) {
                                        AccountInfo accountInfo = new AccountInfo();
                                        accountInfo.setHost(accountInfoVO.getDomain());
                                        accountInfo.setId(IdUtil.getSnowflakeNextId());
                                        accountInfo.setName(accountInfoVO.getSector());
                                        accountInfo.setUpdateTime(date);
                                        accountInfo.setType(value.getValue());
                                        redisUtil.set(redisKey, accountInfo.getName());
                                        accountInfos.add(accountInfo);
                                    }
                                }
                            }
                        }
                        if (CollUtil.isNotEmpty(accountInfos)) {
                            accountInfoService.saveBatch(accountInfos);
                        }
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }

            }
        }
        log.info("同步accountInfo信息完成");
    }


    /**
     * 同步accountInfo信息*
     */
    // @Scheduled(initialDelay = 30 * 1000, fixedDelay = 3 * 60 * 1000)
    public void cacheAccountInfo() {
        boolean b = tryLock(ACCOUNT_INFO_CACHE_LOCK, TimeUnit.SECONDS, 300);
        if (b && (CharSequenceUtil.isEmpty(redisUtil.get(RedisConstant.ACCOUNT_INFO_CAHCE_FLAG)) || accountInfoService.count() != redisUtil.scan(RedisConstant.ACCOUNT_INFO_CAHCE_PREFIX).size())) {
            redisUtil.deleteLike(RedisConstant.ACCOUNT_INFO_CAHCE_PREFIX);
            int count = accountInfoService.count();
            int size = 2000;
            int num = count % size == 0 ? count / size : count / size + 1;
            for (int i = 0; i < num; i++) {
                Page<AccountInfo> page = accountInfoService.page(new Page(i + 1, size));
                List<AccountInfo> records = page.getRecords();
                for (AccountInfo accountInfo : records) {
                    String redisKey = concatAccountInfoKey(String.valueOf(accountInfo.getType()), accountInfo.getHost());
                    redisUtil.set(redisKey, accountInfo.getName());
                }
            }
            redisUtil.set(RedisConstant.ACCOUNT_INFO_CAHCE_FLAG, "OK");
            log.info("缓存accountInfo信息完成");
            redisUtil.delete(ACCOUNT_INFO_CACHE_LOCK);
        }
    }


    public static String concatAccountInfoKey(String type, String host) {
        return RedisConstant.ACCOUNT_INFO_CAHCE_PREFIX + type + ":" + host;

    }
}

