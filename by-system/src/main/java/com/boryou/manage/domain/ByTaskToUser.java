package com.boryou.manage.domain;

import com.boryou.common.annotation.Excel;
import com.boryou.manage.domain.vo.FileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 网评员对象 by_task_to_user
 *
 * <AUTHOR>
 * @date 2023-03-16
 */
@Data
public class ByTaskToUser extends ByNetworkReviewTask {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 主任务ID
     */
    @Excel(name = "主任务ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long taskId;

    /**
     * 网评员任务状态
     */
    @Excel(name = "网评员任务状态")
    private Integer userTaskStatus;

    /**
     * 网评任务状态名称
     */
    private String userTaskStatusName;

    /**
     * 网评员ID
     */
    @Excel(name = "网评员ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long opUserId;

    /**
     * 网评员名称
     */
    private String opUserName;

    /**
     * 网评员部门ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long opUserDeptId;

    /**
     * 网评员部门名称
     */
    private String opUserDeptName;

    /**
     * 网评员任务完成等级
     */
    @Excel(name = "任务绩效分")
    private String taskGrade;

    /**
     * 任务完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "任务完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date finishTime;

    /**
     * 任务文件ID
     */
    @Excel(name = "任务文件ID")
    private String fileId;

    /**
     * 任务文件url
     */
    private String fileUrls;

    /**
     * 任务图片ID
     */
    @Excel(name = "任务图片ID")
    private String picId;

    /**
     * 任务图片url
     */
    private String picUrls;

    /**
     * 完成备注信息
     */
    @Excel(name = "备注信息")
    private String remark;

    /**
     * 信息创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "信息创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date ctime;

    /**
     * 创建人ID
     */
    @Excel(name = "创建人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建人头像地址
     */
    private String createUserAvatar;

    /**
     * 任务已发布天数
     */
    private String taskAge;


    /**
     * 查询开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String selectStartTime;

    /**
     * 查询结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String selectEndTime;

    /**
     * 排序方式
     */
    private String sortWay;

    /**
     * 文件上传时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String fileCTime;

    /**
     * 任务编码增加排序
     */
    private String taskNumSort;
    private List<FileVO> files;
}
