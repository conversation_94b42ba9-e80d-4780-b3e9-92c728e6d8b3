package com.boryou.common.utils.ip;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.boryou.common.config.BoryouConfig;
import com.boryou.common.constant.Constants;
import com.boryou.common.core.redis.RedisCache;
import com.boryou.common.utils.StringUtils;
import com.boryou.common.utils.http.HttpUtils;
import com.boryou.common.utils.spring.SpringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;

/**
 * 获取地址类
 *
 * <AUTHOR>
 */
public class AddressUtils {
    // IP地址查询
    public static final String IP_URL = "http://whois.pconline.com.cn/ipJson.jsp";
    // 未知地址
    public static final String UNKNOWN = "未知地点";
    private static final Logger log = LoggerFactory.getLogger(AddressUtils.class);

    private static final String ipKey = "yq-vsboryou:loginips:";

    public static String getRealAddressByIP(String ip) {
        String address = UNKNOWN;
//        ip="***********"; //测试
        // 内网不查询
        if (IpUtils.internalIp(ip)) {
            return "内网IP";
        }
        if (BoryouConfig.isAddressEnabled()) {
            try {
                return getIPAreaName(ip);
            } catch (Exception e) {
                log.error("获取地理位置异常 {}", ip);
            }
        }
        return address;
    }

    private static String getIPAreaName(String ip) {
        Object cacheObject = SpringUtils.getBean(RedisCache.class).getCacheObject(ipKey + ip);
        if (ObjectUtil.isNotEmpty(cacheObject)) {
            return cacheObject.toString();
        }
        String rspStr = HttpUtils.sendGet(IP_URL, "ip=" + ip + "&json=true", Constants.GBK);
        if (StringUtils.isEmpty(rspStr)) {
            log.error("获取地理位置异常 {}", ip);
            return UNKNOWN;
        }
        JSONObject obj = JSONObject.parseObject(rspStr);
        String region = obj.getString("pro");
        String city = obj.getString("city");
        String format = String.format("%s %s", region, city);
        SpringUtils.getBean(RedisCache.class).setCacheObject(ipKey + ip, format, 1, TimeUnit.DAYS);
        return format;
    }

    public static void main(String[] args) {
        String realAddressByIP = getRealAddressByIP("************, **********");
        System.out.println(realAddressByIP);
    }
}
