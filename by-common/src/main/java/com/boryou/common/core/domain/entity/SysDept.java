package com.boryou.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.boryou.common.core.domain.BaseEntity;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 部门表 sys_dept
 *
 * <AUTHOR>
 */
@Data
public class SysDept extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 父部门ID
     */
    private Long parentId;

    /**
     * 祖级列表
     */
    private String ancestors;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 显示顺序
     */
    private String orderNum;

    /**
     * 负责人
     */
    private String leader;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 部门状态:0正常,1停用
     */
    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 父部门名称
     */
    @TableField(exist = false)
    private String parentName;

    /**
     * 系统标识码
     */
    @TableField(exist = false)
    private String systemId;

    /**
     * 系统名称
     */
    @TableField(exist = false)
    private String systemName;

    /**
     * 标题
     */
    @TableField(exist = false)
    private String title;

    /**
     * 系统链接
     */
    @TableField(exist = false)
    private String systemLink;

    /**
     * 标题logo url
     */
    @TableField(exist = false)
    private String logo;

    /**
     * 标题logo url
     */
    @TableField(exist = false)
    private String titleLogo;

    /**
     * 登录页背景1
     */
    @TableField(exist = false)
    private String loginBg1;

    /**
     * 登录页背景2
     */
    @TableField(exist = false)
    private String loginBg2;

    /**
     * 子部门
     */
    @TableField(exist = false)
    private List<SysDept> children = new ArrayList<SysDept>();

    @TableField(exist = false)
    private Long[] roleIds;
    @TableField(exist = false)
    private Long messageTemplateId;
    @TableField(exist = false)
    private String areaCode;

}
